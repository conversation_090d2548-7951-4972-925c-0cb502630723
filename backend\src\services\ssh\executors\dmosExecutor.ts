import { NodeSSH } from 'node-ssh';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos DmOS/Datacom
 */
export class DmosExecutor extends BaseExecutor {
  constructor(ssh: NodeSSH) {
    super(ssh);
  }

  /**
   * Executa um comando em um dispositivo DmOS
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando com múltiplas linhas em dispositivo DmOS, usando modo especial');
        return await this.executeDmosMultilineCommand(command);
      }

      // Limpar o comando para remover caracteres invisíveis e espaços extras
      const cleanCommand = command.trim().replace(/\s+/g, ' ');

      Logger.log(`Executando comando DmOS: ${cleanCommand.substring(0, 50)}${cleanCommand.length > 50 ? '...' : ''}`);

      // Para dispositivos DmOS, usar shell interativo com abordagem específica
      return await this.executeDmosCommand(cleanCommand);
    } catch (error) {
      Logger.error('Erro ao executar comando DmOS:', error);
      throw new Error(`Falha ao executar comando DmOS: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Executa um comando em um dispositivo DmOS usando shell interativo
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  private executeDmosCommand(command: string): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      let shell: any = null;
      let commandTimeout: NodeJS.Timeout | null = null;
      let globalTimeout: NodeJS.Timeout | null = null;
      let commandSent: boolean = false;

      // Calcular timeout dinâmico para comando único (pode conter múltiplos subcomandos)
      // Estimar a complexidade do comando contando o número de operações
      const commandComplexity = command.split(';').length + command.split('|').length;
      const MAX_EXECUTION_TIME = this.calculateDynamicTimeout(commandComplexity);
      const INACTIVITY_TIMEOUT = 8000; // 8 segundos
      const COMMAND_COMPLETION_DELAY = 3000; // 3 segundos para capturar toda a saída

      Logger.log(`Usando timeout dinâmico de ${MAX_EXECUTION_TIME}ms para comando DmOS com complexidade ${commandComplexity}`)

      try {
        let output = '';
        let errorOutput = '';
        let lastDataTime = Date.now();
        let commandCompleted = false;

        // Função para limpar recursos
        const cleanup = () => {
          if (commandTimeout) {
            clearTimeout(commandTimeout);
            commandTimeout = null;
          }

          if (globalTimeout) {
            clearTimeout(globalTimeout);
            globalTimeout = null;
          }

          if (shell) {
            // Remover todos os listeners para evitar vazamentos de memória
            shell.removeAllListeners('data');
            shell.removeAllListeners('error');
            shell.removeAllListeners('close');
            shell.removeAllListeners('end');

            if (!shell.ended) {
              try {
                shell.end();
              } catch (e) {
                Logger.error('Erro ao fechar shell:', e);
              }
            }
            shell = null;
          }
        };

        // Timeout global para garantir que o comando não execute indefinidamente
        globalTimeout = setTimeout(() => {
          Logger.warn(`Timeout global atingido após ${MAX_EXECUTION_TIME}ms para o comando: ${command}`);
          cleanup();
          resolve({
            stdout: output,
            stderr: errorOutput + '\n[ERRO] Timeout global atingido após 60 segundos',
            code: 124 // Código de timeout
          });
        }, MAX_EXECUTION_TIME);

        // Função para verificar se o comando foi concluído por inatividade
        const checkCompletion = () => {
          const now = Date.now();
          const timeSinceLastData = now - lastDataTime;

          if (timeSinceLastData >= INACTIVITY_TIMEOUT && !commandCompleted) {
            Logger.log(`Comando concluído após ${timeSinceLastData}ms de inatividade`);
            commandCompleted = true;
            cleanup();
            resolve({
              stdout: output,
              stderr: errorOutput,
              code: 0
            });
          } else if (!commandCompleted) {
            // Reagendar verificação
            commandTimeout = setTimeout(checkCompletion, 1000);
          }
        };

        // Iniciar shell interativo com configurações específicas para DmOS
        this.ssh.requestShell({
          term: 'vt100',
          rows: 24,
          cols: 80
        }).then(shellInstance => {
          shell = shellInstance;

          shell.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            output += chunk;
            lastDataTime = Date.now();
            Logger.log(`stdout (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);

            // Se ainda não enviamos o comando e recebemos o prompt inicial, enviar o comando
            if (!commandSent && (chunk.includes('#') || chunk.includes('$'))) {
              Logger.log('Detectado prompt inicial do DmOS, enviando comando');
              setTimeout(() => {
                if (!commandCompleted && shell) {
                  // Limpar o comando para remover caracteres invisíveis e espaços extras
                  const cleanCommand = command.trim().replace(/\s+/g, ' ');
                  Logger.log(`Enviando comando para DmOS: ${cleanCommand}`);
                  shell.write(cleanCommand + '\n');
                  commandSent = true;

                  // Resetar a variável de saída para capturar apenas a partir deste ponto
                  output = '';
                }
              }, 1000); // Pequeno atraso antes de enviar o comando
              return;
            }

            // Verificar se temos o prompt do DmOS (padrões mais abrangentes)
            if (commandSent && (
                chunk.includes('#') ||
                chunk.includes('$') ||
                chunk.match(/[A-Za-z0-9\-_]+[#\$]\s*$/) || // Padrão genérico para prompt de DmOS
                chunk.includes('DmOS') ||
                chunk.includes('Datacom') ||
                chunk.match(/[\r\n][A-Za-z0-9\-_\.]+[>#]\s*$/))) { // Padrão mais abrangente para prompts
              // Se já enviamos o comando e recebemos o prompt novamente, podemos considerar concluído
              Logger.log('Detectado prompt de conclusão do DmOS');
              setTimeout(() => {
                if (!commandCompleted) {
                  commandCompleted = true;
                  cleanup();
                  resolve({
                    stdout: output,
                    stderr: errorOutput,
                    code: 0
                  });
                }
              }, COMMAND_COMPLETION_DELAY); // Aguardar para capturar toda a saída adicional
            }

            // Verificar se há prompt de paginação e enviar espaço
            if (chunk.includes('--More--') ||
                chunk.includes('-- more --') ||
                chunk.includes('Press any key') ||
                chunk.includes('Press SPACE')) {
              Logger.log('Prompt de paginação detectado, enviando espaço');
              shell.write(' ');
              lastDataTime = Date.now();
            }

            // Verificar se há erro de execução
            if (chunk.includes('Error:') ||
                chunk.includes('Invalid command') ||
                chunk.includes('Unknown command') ||
                chunk.includes('Syntax error')) {
              Logger.log('Erro detectado na execução do comando DmOS:', chunk.trim());
              errorOutput += `\n[ERRO] ${chunk.trim()}`;
              // Não finalizamos imediatamente, deixamos o checkCompletion lidar com isso
            }
          });

          shell.stderr?.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            errorOutput += chunk;
            lastDataTime = Date.now();
            Logger.error(`stderr (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.on('error', (err: Error) => {
            Logger.error('Shell error:', err);
            errorOutput += `\n[ERRO] ${err.message}`;
            cleanup();
            reject(err);
          });

          shell.on('close', () => {
            Logger.log('Shell fechado');
            if (!commandCompleted) {
              commandCompleted = true;
              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          });

          // Aguardar um momento para o shell inicializar
          setTimeout(() => {
            // Enviar um Enter para limpar qualquer prompt
            shell.write('\n');

            // Iniciar verificação de conclusão
            commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT);
          }, 2000); // 2 segundos para inicialização
        }).catch(error => {
          Logger.error('Erro ao criar shell interativo para DmOS:', error);
          cleanup();
          reject(error);
        });
      } catch (error) {
        Logger.error('Erro no shell interativo para DmOS:', error);
        if (shell) {
          // Limpar recursos em caso de erro
          if (commandTimeout) {
            clearTimeout(commandTimeout);
            commandTimeout = null;
          }
          if (globalTimeout) {
            clearTimeout(globalTimeout);
            globalTimeout = null;
          }
          try {
            shell.removeAllListeners();
            if (!shell.ended) shell.end();
          } catch (e) {
            Logger.error('Erro ao limpar shell em caso de erro:', e);
          }
        }
        reject(error);
      }
    });
  }

  /**
   * Executa um comando com múltiplas linhas em um dispositivo DmOS
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  private executeDmosMultilineCommand(command: string): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      Logger.log('Executando comando multilinhas em modo especial para DmOS');

      // Para DmOS, vamos manter uma única sessão e enviar os comandos sequencialmente
      const lines = command.split('\n').filter(line => line.trim() !== '');
      Logger.log(`Executando ${lines.length} comandos em uma única sessão para DmOS`);

      let shell: any = null;
      let commandTimeout: NodeJS.Timeout | null = null;
      let globalTimeout: NodeJS.Timeout | null = null;
      let currentLineIndex = 0;
      let successCount = 0;
      let failureCount = 0;
      let lastCommandTime = 0;
      let forceNextCommandTimeout: NodeJS.Timeout | null = null;

      // Calcular timeout dinâmico com base na quantidade de comandos
      const commandCount = lines.length;
      const MAX_EXECUTION_TIME = this.calculateDynamicTimeout(commandCount);
      const INACTIVITY_TIMEOUT = 10000; // 10 segundos
      const COMMAND_DELAY = 3000; // 3 segundos entre comandos
      const FORCE_NEXT_COMMAND_DELAY = 8000; // 8 segundos para forçar o próximo comando

      Logger.log(`Usando timeout dinâmico de ${MAX_EXECUTION_TIME}ms para ${commandCount} comandos DmOS`)

      let output = '';
      let errorOutput = '';
      let lastDataTime = Date.now();
      let commandCompleted = false;
      let promptReady = false;

      // Função para limpar recursos
      const cleanup = () => {
        if (commandTimeout) {
          clearTimeout(commandTimeout);
          commandTimeout = null;
        }

        if (globalTimeout) {
          clearTimeout(globalTimeout);
          globalTimeout = null;
        }

        if (shell) {
          // Remover todos os listeners para evitar vazamentos de memória
          shell.removeAllListeners('data');
          shell.removeAllListeners('error');
          shell.removeAllListeners('close');
          shell.removeAllListeners('end');

          if (!shell.ended) {
            try {
              shell.end();
            } catch (e) {
              Logger.error('Erro ao fechar shell:', e);
            }
          }
          shell = null;
        }
      };

      // Timeout global para garantir que o comando não execute indefinidamente
      globalTimeout = setTimeout(() => {
        Logger.warn(`Timeout global atingido após ${MAX_EXECUTION_TIME}ms para o script DmOS`);
        cleanup();

        // Adicionar resumo da execução
        const summary = `\n--- Resumo da Execução ---\nComandos executados: ${currentLineIndex}/${lines.length}\nSucesso: ${successCount}\nFalhas: ${failureCount}\n`;
        output += summary;

        resolve({
          stdout: output,
          stderr: errorOutput + '\n[ERRO] Timeout global atingido após 180 segundos',
          code: 124 // Código de timeout
        });
      }, MAX_EXECUTION_TIME);

      // Função para verificar se o comando foi concluído por inatividade
      const checkCompletion = () => {
        const now = Date.now();
        const timeSinceLastData = now - lastDataTime;

        if (timeSinceLastData >= INACTIVITY_TIMEOUT && !commandCompleted) {
          Logger.log(`Script concluído após ${timeSinceLastData}ms de inatividade`);
          commandCompleted = true;

          // Adicionar resumo da execução
          const summary = `\n--- Resumo da Execução ---\nComandos executados: ${currentLineIndex}/${lines.length}\nSucesso: ${successCount}\nFalhas: ${failureCount}\n`;
          output += summary;

          cleanup();
          resolve({
            stdout: output,
            stderr: errorOutput,
            code: 0
          });
        } else if (!commandCompleted) {
          // Reagendar verificação
          commandTimeout = setTimeout(checkCompletion, 1000);
        }
      };

      // Função para cancelar o timeout de forçar o próximo comando
      const cancelForceNextCommand = () => {
        if (forceNextCommandTimeout) {
          clearTimeout(forceNextCommandTimeout);
          forceNextCommandTimeout = null;
        }
      };

      // Função para enviar o próximo comando
      const sendNextCommand = () => {
        // Cancelar qualquer timeout pendente para forçar o próximo comando
        cancelForceNextCommand();

        if (currentLineIndex >= lines.length || commandCompleted) {
          Logger.log('Todos os comandos foram enviados');

          // Se todos os comandos foram enviados, aguardar mais um pouco e finalizar
          setTimeout(() => {
            if (!commandCompleted) {
              Logger.log('Todos os comandos foram processados, finalizando');
              commandCompleted = true;

              // Adicionar resumo da execução
              const summary = `\n--- Resumo da Execução ---\nComandos executados: ${currentLineIndex}/${lines.length}\nSucesso: ${successCount}\nFalhas: ${failureCount}\n`;
              output += summary;

              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          }, 5000); // Aguardar 5 segundos após o último comando

          return;
        }

        const line = lines[currentLineIndex].trim();
        Logger.log(`Enviando comando ${currentLineIndex+1}/${lines.length} para DmOS: ${line}`);

        if (shell && !commandCompleted) {
          // Adicionar separador para o comando atual
          output += `\n--- Comando: ${line} ---\n`;

          // Limpar o comando para remover caracteres invisíveis e espaços extras
          const cleanCommand = line.trim().replace(/\s+/g, ' ');
          shell.write(cleanCommand + '\n');
          lastDataTime = Date.now(); // Resetar o tempo do último dado
          lastCommandTime = Date.now(); // Registrar quando o comando foi enviado
          currentLineIndex++;
          successCount++;

          // Configurar um timeout para forçar o envio do próximo comando se não detectarmos o prompt
          forceNextCommandTimeout = setTimeout(() => {
            Logger.log(`Forçando envio do próximo comando após ${FORCE_NEXT_COMMAND_DELAY}ms sem detecção de prompt`);
            // Enviar um Enter para tentar obter o prompt
            if (shell && !commandCompleted) {
              shell.write('\n');

              // Aguardar um pouco e enviar o próximo comando
              setTimeout(() => {
                if (!commandCompleted) {
                  Logger.log('Continuando com o próximo comando após timeout');
                  sendNextCommand();
                }
              }, 1000);
            }
          }, FORCE_NEXT_COMMAND_DELAY);
        }
      };

      try {
        // Iniciar shell interativo com configurações específicas para DmOS
        this.ssh.requestShell({
          term: 'vt100',
          rows: 24,
          cols: 120
        }).then(shellInstance => {
          shell = shellInstance;

          shell.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            output += chunk;
            lastDataTime = Date.now();
            Logger.log(`stdout (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);

            // Verificar se temos o prompt do DmOS (padrões mais abrangentes)
            const hasPrompt = (
                chunk.includes('#') ||
                chunk.includes('$') ||
                chunk.match(/[A-Za-z0-9\-_]+[#\$]\s*$/) || // Padrão genérico para prompt de DmOS
                chunk.includes('DmOS') ||
                chunk.includes('Datacom') ||
                chunk.match(/[\r\n][A-Za-z0-9\-_\.]+[>#]\s*$/) || // Padrão mais abrangente para prompts
                chunk.match(/[\r\n][A-Za-z0-9\-_\.]+[>]\s*$/) || // Prompt com >
                chunk.match(/[\r\n][A-Za-z0-9\-_\.]+[#]\s*$/) || // Prompt com #
                chunk.match(/[\r\n]\S+[@][\w\-\.]+[:#]\s*$/) || // Prompt estilo Linux
                chunk.match(/[\r\n]\w+\s*[>#\$]\s*$/) // Prompt simples
            );

            if (hasPrompt) {
              // Cancelar qualquer timeout pendente para forçar o próximo comando
              cancelForceNextCommand();

              // Marcar que o prompt está pronto para receber comandos
              promptReady = true;

              Logger.log('Detectado prompt DmOS, aguardando antes de enviar próximo comando');

              // Aguardar um momento antes de enviar o próximo comando
              setTimeout(() => {
                if (!commandCompleted && promptReady) {
                  promptReady = false; // Resetar o estado do prompt
                  sendNextCommand();
                }
              }, COMMAND_DELAY); // Aguardar entre comandos
            }

            // Verificar se passou muito tempo desde o último comando sem detectar prompt
            const timeSinceLastCommand = Date.now() - lastCommandTime;
            if (lastCommandTime > 0 && timeSinceLastCommand > FORCE_NEXT_COMMAND_DELAY && !hasPrompt && !promptReady) {
              Logger.log(`Muito tempo sem detectar prompt (${timeSinceLastCommand}ms), forçando próximo comando`);
              promptReady = true;
              setTimeout(() => {
                if (!commandCompleted && promptReady) {
                  promptReady = false;
                  sendNextCommand();
                }
              }, 1000);
            }

            // Verificar se há erro de execução
            if (chunk.includes('Error:') ||
                chunk.includes('Invalid command') ||
                chunk.includes('Unknown command') ||
                chunk.includes('Syntax error')) {
              Logger.log('Erro detectado na execução do comando DmOS:', chunk.trim());
              errorOutput += `\n[ERRO] ${chunk.trim()}`;
              failureCount++;
              successCount--;

              // Continuar com o próximo comando após um tempo maior
              setTimeout(() => {
                if (!commandCompleted) {
                  Logger.log('Tentando continuar após erro...');
                  promptReady = true; // Forçar o estado do prompt para pronto
                  sendNextCommand();
                }
              }, 3000); // Aguardar 3 segundos após erro
            }

            // Verificar se há prompt de paginação e enviar espaço
            if (chunk.includes('--More--') ||
                chunk.includes('-- more --') ||
                chunk.includes('Press any key') ||
                chunk.includes('Press SPACE')) {
              Logger.log('Prompt de paginação detectado, enviando espaço');
              shell.write(' ');
              lastDataTime = Date.now();
            }
          });

          shell.stderr?.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            errorOutput += chunk;
            lastDataTime = Date.now();
            Logger.error(`stderr (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.on('error', (err: Error) => {
            Logger.error('Shell error:', err);
            errorOutput += `\n[ERRO] ${err.message}`;
            failureCount++;

            // Se ocorrer um erro, tentar continuar com o próximo comando
            setTimeout(() => {
              if (!commandCompleted) {
                Logger.log('Tentando continuar após erro...');
                promptReady = true; // Forçar o estado do prompt para pronto
                sendNextCommand();
              }
            }, 3000); // Aguardar 3 segundos após erro
          });

          shell.on('close', () => {
            Logger.log('Shell fechado');
            if (!commandCompleted) {
              commandCompleted = true;

              // Adicionar resumo da execução
              const summary = `\n--- Resumo da Execução ---\nComandos executados: ${currentLineIndex}/${lines.length}\nSucesso: ${successCount}\nFalhas: ${failureCount}\n`;
              output += summary;

              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          });

          // Aguardar um momento para o shell inicializar
          setTimeout(() => {
            // Enviar um Enter para limpar qualquer prompt
            shell.write('\n');

            // Aguardar um pouco e enviar outro Enter para garantir que temos o prompt
            setTimeout(() => {
              if (!commandCompleted && shell) {
                Logger.log('Enviando segundo Enter para garantir prompt');
                shell.write('\n');

                // Iniciar o envio de comandos após um breve atraso
                setTimeout(() => {
                  if (!commandCompleted) {
                    // Iniciar o envio de comandos
                    sendNextCommand();
                  }
                }, 2000);
              }

              // Iniciar verificação de conclusão
              commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT);
            }, 2000);
          }, 3000); // 3 segundos para inicialização
        }).catch(error => {
          Logger.error('Erro ao criar shell interativo para DmOS:', error);
          cleanup();
          reject(error);
        });
      } catch (error) {
        Logger.error('Erro no shell interativo para DmOS:', error);
        cleanup();
        reject(error);
      }
    });
  }
}
