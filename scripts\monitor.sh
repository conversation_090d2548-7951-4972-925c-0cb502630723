#!/bin/bash
# Script de monitoramento e reinicialização para o sistema Sem Fronteiras
# Este script deve ser executado periodicamente via cron para garantir a estabilidade do sistema

# Configurações
LOG_FILE="/var/log/sem-fronteiras-monitor.log"
MAX_MEMORY_PERCENT=90
MAX_CPU_PERCENT=90
RESTART_TIMEOUT=3600  # Tempo mínimo entre reinicializações (1 hora)
LAST_RESTART_FILE="/tmp/sem-fronteiras-last-restart"

# Função para registrar mensagens no log
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Verificar se o script está sendo executado como root
if [ "$(id -u)" -ne 0 ]; then
  log "ERRO: Este script deve ser executado como root"
  exit 1
fi

# Criar arquivo de log se não existir
touch "$LOG_FILE"

log "Iniciando monitoramento do sistema Sem Fronteiras"

# Verificar se o Docker está em execução
if ! systemctl is-active --quiet docker; then
  log "ALERTA: Docker não está em execução. Tentando iniciar..."
  systemctl start docker
  sleep 5
  
  if ! systemctl is-active --quiet docker; then
    log "ERRO: Falha ao iniciar o Docker"
    exit 1
  else
    log "Docker iniciado com sucesso"
  fi
fi

# Verificar se os contêineres estão em execução
BACKEND_CONTAINER="sem-fronteiras-ssh-backend-1"
FRONTEND_CONTAINER="sem-fronteiras-ssh-frontend-1"
POSTGRES_CONTAINER="sem-fronteiras-ssh-postgres-1"
REDIS_CONTAINER="sem-fronteiras-ssh-redis-1"

check_container() {
  local container_name="$1"
  if ! docker ps --format '{{.Names}}' | grep -q "$container_name"; then
    log "ALERTA: Contêiner $container_name não está em execução"
    return 1
  fi
  return 0
}

# Verificar todos os contêineres
CONTAINERS_OK=true
for container in "$BACKEND_CONTAINER" "$FRONTEND_CONTAINER" "$POSTGRES_CONTAINER" "$REDIS_CONTAINER"; do
  if ! check_container "$container"; then
    CONTAINERS_OK=false
  fi
done

# Verificar uso de memória do contêiner backend
BACKEND_MEMORY_USAGE=$(docker stats "$BACKEND_CONTAINER" --no-stream --format "{{.MemPerc}}" | sed 's/%//')
if [ -n "$BACKEND_MEMORY_USAGE" ] && [ "$(echo "$BACKEND_MEMORY_USAGE > $MAX_MEMORY_PERCENT" | bc -l)" -eq 1 ]; then
  log "ALERTA: Uso de memória do backend está alto: ${BACKEND_MEMORY_USAGE}%"
  CONTAINERS_OK=false
fi

# Verificar uso de CPU do contêiner backend
BACKEND_CPU_USAGE=$(docker stats "$BACKEND_CONTAINER" --no-stream --format "{{.CPUPerc}}" | sed 's/%//')
if [ -n "$BACKEND_CPU_USAGE" ] && [ "$(echo "$BACKEND_CPU_USAGE > $MAX_CPU_PERCENT" | bc -l)" -eq 1 ]; then
  log "ALERTA: Uso de CPU do backend está alto: ${BACKEND_CPU_USAGE}%"
  CONTAINERS_OK=false
fi

# Verificar se o backend está respondendo
BACKEND_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health || echo "000")
if [ "$BACKEND_HEALTH" != "200" ]; then
  log "ALERTA: Backend não está respondendo corretamente. Código HTTP: $BACKEND_HEALTH"
  CONTAINERS_OK=false
fi

# Verificar se é necessário reiniciar os contêineres
if [ "$CONTAINERS_OK" = false ]; then
  # Verificar se já houve uma reinicialização recente
  if [ -f "$LAST_RESTART_FILE" ]; then
    LAST_RESTART=$(cat "$LAST_RESTART_FILE")
    NOW=$(date +%s)
    ELAPSED=$((NOW - LAST_RESTART))
    
    if [ "$ELAPSED" -lt "$RESTART_TIMEOUT" ]; then
      log "AVISO: Última reinicialização ocorreu há menos de $(($RESTART_TIMEOUT / 60)) minutos. Pulando reinicialização."
      exit 0
    fi
  fi
  
  log "Iniciando reinicialização dos contêineres..."
  
  # Salvar timestamp da reinicialização
  date +%s > "$LAST_RESTART_FILE"
  
  # Mudar para o diretório do projeto
  cd /var/www/sem-fronteiras-ssh || {
    log "ERRO: Diretório do projeto não encontrado"
    exit 1
  }
  
  # Reiniciar os contêineres
  if docker-compose restart; then
    log "Contêineres reiniciados com sucesso"
  else
    log "ERRO: Falha ao reiniciar contêineres"
    
    # Tentar parar e iniciar os contêineres
    log "Tentando parar e iniciar os contêineres..."
    docker-compose down
    sleep 10
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
      log "Contêineres iniciados com sucesso após parada forçada"
    else
      log "ERRO CRÍTICO: Falha ao iniciar contêineres após parada forçada"
    fi
  fi
else
  log "Todos os contêineres estão funcionando corretamente"
fi

log "Monitoramento concluído"
exit 0
