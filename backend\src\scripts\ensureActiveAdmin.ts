import { PrismaClient } from '@prisma/client'

/**
 * Este script verifica se existe pelo menos um administrador ativo no sistema.
 * Se não existir, ele ativa o primeiro administrador encontrado.
 * Deve ser executado na inicialização do servidor.
 */
async function ensureActiveAdmin() {
  const prisma = new PrismaClient()
  
  try {
    console.log('Verificando se existe pelo menos um administrador ativo...')
    
    // Verificar se existe pelo menos um administrador ativo usando SQL direto
    const activeAdmins = await prisma.$queryRaw<Array<{ count: bigint }>>`
      SELECT COUNT(*) as count FROM "User" 
      WHERE role = 'ADMIN' AND active = true
    `
    const activeAdminCount = Number(activeAdmins[0]?.count || 0)
    
    if (activeAdminCount === 0) {
      console.log('Nenhum administrador ativo encontrado. Buscando administradores...')
      
      // Buscar o primeiro administrador (mesmo que inativo)
      const admin = await prisma.user.findFirst({
        where: {
          role: 'ADMIN'
        }
      })
      
      if (admin) {
        console.log(`Reativando administrador: ${admin.name} (${admin.email})`)
        
        // Reativar o administrador usando o método update do Prisma
        await prisma.user.update({
          where: { id: admin.id },
          data: { active: true } as any
        })
        
        console.log('Administrador reativado com sucesso!')
      } else {
        console.log('Nenhum administrador encontrado no sistema!')
      }
    } else {
      console.log(`${activeAdminCount} administrador(es) ativo(s) encontrado(s).`)
    }
  } catch (error) {
    console.error('Erro ao verificar administradores ativos:', error)
  } finally {
    await prisma.$disconnect()
  }
}

export default ensureActiveAdmin 