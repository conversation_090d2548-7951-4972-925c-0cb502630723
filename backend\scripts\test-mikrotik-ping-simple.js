const { NodeSSH } = require('node-ssh');
const MikrotikExecutor = require('../src/services/ssh/executors/mikrotikExecutor').MikrotikExecutor;
const Logger = require('../src/utils/logger');

/**
 * Script simples para testar a correção do comando PING no Mikrotik
 * Foca especificamente no problema de captura das estatísticas
 */

async function testMikrotikPingSimple() {
  Logger.log('🧪 === TESTE SIMPLES DE PING MIKROTIK ===');
  
  const ssh = new NodeSSH();
  
  try {
    // Conectar ao Mikrotik
    Logger.log('🔌 Conectando ao Mikrotik...');
    await ssh.connect({
      host: '***********',
      username: 'admin',
      password: '88701181Sem*',
      port: 22,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1'],
        compress: ['none']
      }
    });
    
    Logger.log('✅ Conectado com sucesso!');
    
    // Criar executor
    const executor = new MikrotikExecutor(ssh);
    
    // Teste único e simples
    Logger.log('\n🎯 === TESTE: Ping com count=2 ===');
    Logger.log('📝 Comando: /ping address=******* count=2');
    
    try {
      const startTime = Date.now();
      
      // Executar o comando ping
      Logger.log('🚀 Executando comando ping...');
      const result = await executor.executeCommand('/ping address=******* count=2');
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      Logger.log(`⏱️ Duração: ${duration}ms`);
      Logger.log(`📊 Código de saída: ${result.code}`);
      
      // Analisar a saída
      if (result.stdout) {
        Logger.log('\n📤 === SAÍDA COMPLETA ===');
        console.log('--- INÍCIO DA SAÍDA ---');
        console.log(result.stdout);
        console.log('--- FIM DA SAÍDA ---');
        
        // Verificar padrões específicos
        const hasHeader = result.stdout.includes('SEQ HOST') || result.stdout.includes('SEQ') && result.stdout.includes('HOST');
        const hasStatistics = result.stdout.includes('sent=') && result.stdout.includes('received=');
        const hasPacketData = result.stdout.match(/\d+\s+\d+\.\d+\.\d+\.\d+/);
        
        Logger.log('\n🔍 === ANÁLISE DA SAÍDA ===');
        Logger.log(`📋 Cabeçalho (SEQ HOST): ${hasHeader ? '✅ ENCONTRADO' : '❌ NÃO ENCONTRADO'}`);
        Logger.log(`📊 Estatísticas (sent=/received=): ${hasStatistics ? '✅ ENCONTRADO' : '❌ NÃO ENCONTRADO'}`);
        Logger.log(`📦 Dados de pacotes: ${hasPacketData ? '✅ ENCONTRADO' : '❌ NÃO ENCONTRADO'}`);
        
        // Verificar se contém as estatísticas específicas esperadas
        const hasSent2 = result.stdout.includes('sent=2');
        const hasPacketLoss = result.stdout.includes('packet-loss=');
        const hasRttStats = result.stdout.includes('min-rtt=') || result.stdout.includes('avg-rtt=');
        
        Logger.log(`🎯 Sent=2: ${hasSent2 ? '✅ ENCONTRADO' : '❌ NÃO ENCONTRADO'}`);
        Logger.log(`📉 Packet-loss: ${hasPacketLoss ? '✅ ENCONTRADO' : '❌ NÃO ENCONTRADO'}`);
        Logger.log(`⏱️ RTT Stats: ${hasRttStats ? '✅ ENCONTRADO' : '❌ NÃO ENCONTRADO'}`);
        
        // Resultado final
        if (result.code === 0 && hasStatistics && hasHeader) {
          Logger.log('\n🎉 ✅ TESTE PASSOU - Ping funcionando corretamente!');
          Logger.log('🔧 As correções implementadas resolveram o problema!');
        } else {
          Logger.error('\n❌ TESTE FALHOU - Ainda há problemas na captura do ping');
          
          if (!hasHeader) {
            Logger.error('  - Cabeçalho do ping não foi capturado');
          }
          if (!hasStatistics) {
            Logger.error('  - Estatísticas finais não foram capturadas');
          }
          if (result.code !== 0) {
            Logger.error(`  - Código de saída incorreto: ${result.code}`);
          }
        }
        
      } else {
        Logger.error('❌ TESTE FALHOU - Nenhuma saída recebida');
      }
      
      if (result.stderr) {
        Logger.warn('\n⚠️ Erros detectados:');
        console.log(result.stderr);
      }
      
    } catch (error) {
      Logger.error(`❌ TESTE FALHOU - Erro: ${error.message}`);
      
      // Verificar se é timeout
      if (error.message.includes('timeout') || error.message.includes('TIMEOUT')) {
        Logger.error('🕐 Problema: Comando ainda está fazendo timeout');
        Logger.error('💡 Possível causa: Comando não está sendo executado no Mikrotik');
      }
    }
    
  } catch (error) {
    Logger.error('❌ Erro na conexão:', error);
  } finally {
    // Fechar conexão
    if (ssh.isConnected()) {
      ssh.dispose();
      Logger.log('🔌 Conexão SSH fechada');
    }
  }
  
  Logger.log('\n🏁 === TESTE SIMPLES CONCLUÍDO ===');
  Logger.log('📋 Próximos passos:');
  Logger.log('  1. Se o teste passou: ✅ Problema resolvido!');
  Logger.log('  2. Se ainda falhou: 🔧 Verificar logs detalhados para mais ajustes');
}

// Executar o teste
if (require.main === module) {
  testMikrotikPingSimple().catch(console.error);
}

module.exports = { testMikrotikPingSimple };
