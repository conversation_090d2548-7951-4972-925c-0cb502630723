export interface SSHServer {
  id: string
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  os: 'LINUX' | 'WINDOWS'
  deviceType: 'NOKIA' | 'HUAW<PERSON>' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  commands: Command[]
  userId: string
  createdAt: string
  updatedAt: string
}

export interface Command {
  id: string
  name: string
  command: string
  description?: string
  serverId: string
  order: number
  createdAt: string
  updatedAt: string
}

export interface CommandResult {
  stdout?: string
  stderr?: string
  code?: number
}

export interface CreateServerDTO {
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  deviceType?: 'NOKIA' | 'HUAWEI' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  commands: {
    name: string
    command: string
    description?: string
    order?: number
  }[]
}

export interface UpdateServerDTO {
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  deviceType?: 'NOKIA' | 'H<PERSON><PERSON><PERSON><PERSON>' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  commands: {
    id?: string
    name: string
    command: string
    description?: string
    order?: number
  }[]
}

export interface ExecuteCommandDTO {
  commandId: string
}

export interface ServerUserDTO {
  userId: string
  serverId: string
}

export interface ServerUserResponse {
  id: string
  userId: string
  serverId: string
  user: {
    id: string
    name: string
    email: string
  }
  server: {
    id: string
    name: string
  }
  createdAt: string
  updatedAt: string
}