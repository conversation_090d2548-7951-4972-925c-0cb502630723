#!/bin/bash
# Script universal para atualizar o sistema
# Funciona tanto em ambiente de produção quanto local
# Este script deve ser executado no HOST, não dentro do contêiner

# Verificar se está sendo executado dentro de um contêiner Docker
if [ -f /.dockerenv ]; then
  echo "ERRO: Este script deve ser executado no host, não dentro do contêiner."
  echo "Por favor, execute este script diretamente no sistema host."
  exit 1
fi

# Detectar ambiente
if [ -d "/var/www/sem-fronteiras-ssh" ]; then
  echo "Executando em ambiente de PRODUÇÃO"
  AMBIENTE="produção"
  # Configurações de produção
  BACKUP_DIR="/var/www/backups"
  PROJETO_DIR="/var/www/sem-fronteiras-ssh"
  POSTGRES_CONTAINER="sem-fronteiras-ssh-postgres-1"
  FAZER_PULL=true
  APLICAR_MIGRACOES=true
else
  echo "Executando em ambiente de DESENVOLVIMENTO"
  AMBIENTE="desenvolvimento"
  # Configurações de desenvolvimento
  BACKUP_DIR="./backups"
  PROJETO_DIR="."
  POSTGRES_CONTAINER="sem-fronteiras-postgres-1"
  # Perguntar se deve fazer pull e migrações em desenvolvimento
  read -p "Deseja fazer git pull? (s/N): " RESPOSTA_PULL
  if [[ "$RESPOSTA_PULL" =~ ^[sS]$ ]]; then
    FAZER_PULL=true
  else
    FAZER_PULL=false
  fi

  read -p "Deseja aplicar migrações do Prisma? (s/N): " RESPOSTA_MIGRACOES
  if [[ "$RESPOSTA_MIGRACOES" =~ ^[sS]$ ]]; then
    APLICAR_MIGRACOES=true
  else
    APLICAR_MIGRACOES=false
  fi
fi

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.sql"

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"

# 1. Fazer backup do banco de dados
echo "[$(date)] Iniciando backup do banco de dados..."
if docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d sem_fronteiras > "$BACKUP_FILE" 2>/dev/null; then
  echo "[$(date)] Backup concluído com sucesso: $BACKUP_FILE"
else
  echo "[$(date)] AVISO: Falha ao criar backup. Continuando..."
  # Criar um arquivo vazio para evitar erros
  touch "$BACKUP_FILE"
fi

# 2. Atualizar o repositório (git pull)
if [ "$FAZER_PULL" = true ]; then
  echo "[$(date)] Atualizando o repositório..."
  cd "$PROJETO_DIR"
  git pull
else
  echo "[$(date)] Pulando atualização do repositório."
fi

# 3. Aplicar migrações do Prisma
if [ "$APLICAR_MIGRACOES" = true ]; then
  echo "[$(date)] Aplicando migrações do Prisma..."

  # Determinar o nome do contêiner backend com base no ambiente
  if [ "$AMBIENTE" = "produção" ]; then
    BACKEND_CONTAINER="sem-fronteiras-ssh-backend-1"
  else
    BACKEND_CONTAINER="sem-fronteiras-backend-1"
  fi

  # Verificar se os contêineres estão em execução
  if docker ps | grep -q $BACKEND_CONTAINER && docker ps | grep -q $POSTGRES_CONTAINER; then
    echo "[$(date)] Preparando banco de dados para migrações..."

    # Corrigir valores nulos na tabela CommandHistory (se existir)
    echo "[$(date)] Verificando e corrigindo valores nulos na tabela CommandHistory..."
    docker exec $POSTGRES_CONTAINER psql -U postgres -d sem_fronteiras -c "DO \$\$
    BEGIN
      -- Verificar se a tabela CommandHistory existe
      IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'CommandHistory') THEN
        -- Verificar se a coluna commandVersion existe
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'CommandHistory' AND column_name = 'commandVersion') THEN
          -- Atualizar registros com valores nulos
          UPDATE \"CommandHistory\" SET \"commandVersion\" = '1.0' WHERE \"commandVersion\" IS NULL;
          RAISE NOTICE 'Valores nulos na coluna commandVersion foram atualizados para 1.0';
        END IF;
      END IF;
    END
    \$\$;" || echo "[$(date)] AVISO: Não foi possível corrigir valores nulos. Continuando..."

    echo "[$(date)] Executando migrações dentro do contêiner $BACKEND_CONTAINER..."
    # Executar o comando dentro do contêiner
    if docker exec $BACKEND_CONTAINER npx prisma migrate deploy; then
      echo "[$(date)] Migrações aplicadas com sucesso!"
    else
      echo "[$(date)] AVISO: Falha ao aplicar migrações."

      # Tentar resolver problemas comuns de migração
      echo "[$(date)] Tentando resolver problemas de migração..."

      # Listar migrações pendentes
      PENDING_MIGRATIONS=$(docker exec $BACKEND_CONTAINER npx prisma migrate status | grep -i "pending" | awk '{print $2}')

      if [ ! -z "$PENDING_MIGRATIONS" ]; then
        echo "[$(date)] Migrações pendentes encontradas: $PENDING_MIGRATIONS"
        echo "[$(date)] Marcando migrações como aplicadas..."

        # Para cada migração pendente, marcar como aplicada
        for MIGRATION in $PENDING_MIGRATIONS; do
          echo "[$(date)] Marcando migração $MIGRATION como aplicada..."
          docker exec $BACKEND_CONTAINER npx prisma migrate resolve --applied $MIGRATION || echo "[$(date)] Falha ao marcar migração $MIGRATION como aplicada."
        done
      else
        echo "[$(date)] Nenhuma migração pendente encontrada."
      fi

      echo "[$(date)] Continuando com a atualização..."
    fi
  else
    echo "[$(date)] AVISO: Contêineres necessários não encontrados ou não estão em execução. Pulando migrações."
  fi
else
  echo "[$(date)] Pulando aplicação de migrações do Prisma."
fi

# 4. Reiniciar os contêineres
echo "[$(date)] Reiniciando os contêineres..."
cd "$PROJETO_DIR"
docker-compose restart

echo "[$(date)] Atualização concluída com sucesso no ambiente de $AMBIENTE!"
