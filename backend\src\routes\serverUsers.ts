import { FastifyInstance } from 'fastify'
import { verifyJWT } from '../middlewares/auth'
import { verifyAdmin } from '../middlewares/admin'
import { listServerUsers, addServerUser, removeServerUser } from '../controllers/serverUsers'

export async function serverUserRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Listar usuários com acesso a um servidor específico
  app.get('/servers/:serverId/users', listServerUsers)

  // Adicionar acesso de usuário a um servidor (apenas administradores)
  app.post('/server-users', async (request, reply) => {
    // Verificar se o usuário é administrador
    await verifyAdmin(request, reply)
    
    // Se a verificação passar, continuar com a adição do usuário ao servidor
    return addServerUser(request, reply)
  })

  // Remover acesso de usuário a um servidor
  app.delete('/server-users/:id', removeServerUser)
} 