import { api } from '../lib/api'
import { 
  ServerGroup, 
  CreateServerGroupDTO, 
  UpdateServerGroupDTO,
  AddServerToGroupDTO,
  ServerGroupWithServers 
} from '../types/serverGroup'

// Listar grupos do usuário
export async function listServerGroups(): Promise<ServerGroup[]> {
  const response = await api.get('/api/server-groups')
  return response.data.groups
}

// Obter grupo específico
export async function getServerGroup(id: string): Promise<ServerGroupWithServers> {
  const response = await api.get(`/api/server-groups/${id}`)
  return response.data
}

// Criar grupo
export async function createServerGroup(data: CreateServerGroupDTO): Promise<ServerGroup> {
  const response = await api.post('/api/server-groups', data)
  return response.data
}

// Atualizar grupo
export async function updateServerGroup(
  id: string, 
  data: UpdateServerGroupDTO
): Promise<ServerGroup> {
  const response = await api.put(`/api/server-groups/${id}`, data)
  return response.data
}

// Excluir grupo
export async function deleteServerGroup(id: string): Promise<void> {
  await api.delete(`/api/server-groups/${id}`)
}

// Adicionar servidor ao grupo
export async function addServerToGroup(
  groupId: string, 
  data: AddServerToGroupDTO
): Promise<void> {
  await api.post(`/api/server-groups/${groupId}/servers`, data)
}

// Remover servidor do grupo
export async function removeServerFromGroup(
  groupId: string, 
  serverId: string
): Promise<void> {
  await api.delete(`/api/server-groups/${groupId}/servers/${serverId}`)
}
