const { NodeSSH } = require('node-ssh');
const { MikrotikExecutor } = require('../dist/services/ssh/executors/mikrotikExecutor');
const { Logger } = require('../dist/utils/logger');

async function testPingCountFix() {
  const ssh = new NodeSSH();
  
  try {
    // Conectar ao Mikrotik
    Logger.log('🔌 Conectando ao Mikrotik...');
    await ssh.connect({
      host: '***********',
      username: 'admin',
      password: '88701181Sem*',
      port: 22,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1'],
        compress: ['none']
      }
    });
    
    Logger.log('✅ Conectado com sucesso!');
    
    // Criar executor
    const executor = new MikrotikExecutor(ssh);
    
    // Testes específicos para validar correção do parâmetro count
    const tests = [
      {
        name: 'Ping simples sem count - Deve usar count=4 padrão',
        command: 'ping *******',
        expectedCount: 4,
        expectedFormat: '/ping address=******* count=4',
        description: 'Comando simples deve adicionar count=4 padrão'
      },
      {
        name: 'Ping com count=2 - Deve preservar count=2',
        command: 'ping ******* count=2',
        expectedCount: 2,
        expectedFormat: '/ping address=******* count=2',
        description: 'Comando com count específico deve preservar o valor'
      },
      {
        name: 'Ping com count=8 - Deve preservar count=8',
        command: 'ping ******* count=8',
        expectedCount: 8,
        expectedFormat: '/ping address=******* count=8',
        description: 'Comando com count=8 deve enviar exatamente 8 pacotes'
      },
      {
        name: 'Ping com count=1 - Deve preservar count=1',
        command: 'ping ******* count=1',
        expectedCount: 1,
        expectedFormat: '/ping address=******* count=1',
        description: 'Comando com count=1 deve enviar apenas 1 pacote'
      },
      {
        name: 'Ping com sintaxe Mikrotik completa - Deve preservar tudo',
        command: '/ping address=******* count=6 interval=2s',
        expectedCount: 6,
        expectedFormat: '/ping address=******* count=6 interval=2s',
        description: 'Comando já formatado deve ser preservado integralmente'
      },
      {
        name: 'Ping com múltiplos parâmetros - Deve preservar todos',
        command: 'ping ******* count=3 size=128',
        expectedCount: 3,
        expectedFormat: '/ping address=******* count=3 size=128',
        description: 'Comando com múltiplos parâmetros deve preservar todos'
      }
    ];
    
    for (const test of tests) {
      Logger.log(`\n🎯 === ${test.name} ===`);
      Logger.log(`📝 Comando: ${test.command}`);
      Logger.log(`🔢 Count esperado: ${test.expectedCount} pacotes`);
      Logger.log(`📋 Descrição: ${test.description}`);
      Logger.log(`🎯 Formato esperado: ${test.expectedFormat}`);
      
      try {
        const startTime = Date.now();
        
        Logger.log('\n🚀 Executando comando...');
        const result = await executor.executeCommand(test.command);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        Logger.log(`\n⏱️ === RESULTADOS ===`);
        Logger.log(`⏱️ Duração: ${duration}ms`);
        Logger.log(`📊 Código de saída: ${result.code}`);
        
        if (result.stdout) {
          Logger.log('\n📤 === SAÍDA COMPLETA ===');
          console.log(result.stdout);
          Logger.log('=== FIM DA SAÍDA ===\n');
          
          // Análise específica do count
          const lines = result.stdout.split('\n');
          const packetLines = lines.filter(line => /^\s*\d+\s+/.test(line) && line.includes('ms'));
          const statisticsLine = lines.find(line => line.includes('sent=') && line.includes('received='));
          
          Logger.log('🔍 === ANÁLISE DO COUNT ===');
          Logger.log(`  📦 Linhas de pacotes encontradas: ${packetLines.length}`);
          
          if (statisticsLine) {
            const sentMatch = statisticsLine.match(/sent=(\d+)/);
            const receivedMatch = statisticsLine.match(/received=(\d+)/);
            
            const sentCount = sentMatch ? parseInt(sentMatch[1]) : 0;
            const receivedCount = receivedMatch ? parseInt(receivedMatch[1]) : 0;
            
            Logger.log(`  📤 Pacotes enviados: ${sentCount}`);
            Logger.log(`  📥 Pacotes recebidos: ${receivedCount}`);
            
            // Verificar se o count está correto
            const countCorrect = sentCount === test.expectedCount;
            const packetsCorrect = packetLines.length <= test.expectedCount; // Pode ser menor se houver timeouts
            
            Logger.log('\n🏁 === VERIFICAÇÃO ===');
            Logger.log(`  ${countCorrect ? '✅' : '❌'} Count correto: ${countCorrect ? 'SIM' : 'NÃO'} (esperado: ${test.expectedCount}, obtido: ${sentCount})`);
            Logger.log(`  ${packetsCorrect ? '✅' : '❌'} Número de pacotes: ${packetsCorrect ? 'CORRETO' : 'INCORRETO'} (linhas: ${packetLines.length})`);
            
            if (countCorrect && packetsCorrect) {
              Logger.log('🎉 TESTE PASSOU - Count preservado corretamente!');
            } else {
              Logger.log('❌ TESTE FALHOU - Count não foi preservado!');
              if (!countCorrect) {
                Logger.log(`   • Esperado: ${test.expectedCount} pacotes`);
                Logger.log(`   • Obtido: ${sentCount} pacotes`);
              }
            }
          } else {
            Logger.log('⚠️ Linha de estatísticas não encontrada');
            Logger.log('❓ Não foi possível verificar o count');
          }
          
          // Verificar se há dados de pacotes individuais
          if (packetLines.length > 0) {
            Logger.log('\n📊 === DADOS DOS PACOTES ===');
            packetLines.forEach((line, index) => {
              Logger.log(`  Pacote ${index + 1}: ${line.trim()}`);
            });
          }
          
        } else {
          Logger.log('❌ Nenhuma saída retornada');
        }
        
        if (result.stderr) {
          Logger.log('\n⚠️ === STDERR ===');
          console.log(result.stderr);
          Logger.log('=== FIM STDERR ===\n');
        }
        
      } catch (error) {
        Logger.error(`❌ Erro no teste: ${error.message}`);
      }
      
      // Pausa entre testes
      Logger.log('⏸️ Aguardando 3 segundos antes do próximo teste...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    Logger.log('\n🏁 === RESUMO DOS TESTES ===');
    Logger.log('✅ Testes de correção do parâmetro count concluídos');
    Logger.log('📊 Recursos testados:');
    Logger.log('  • Preservação de count específico (count=2, count=8, etc.)');
    Logger.log('  • Adição de count=4 padrão quando não especificado');
    Logger.log('  • Preservação de múltiplos parâmetros (count, size, interval)');
    Logger.log('  • Manutenção de comandos já formatados');
    Logger.log('  • Conversão correta de sintaxe simples para Mikrotik');
    
  } catch (error) {
    Logger.error('❌ Erro durante os testes:', error);
  } finally {
    try {
      await ssh.dispose();
      Logger.log('🔌 Conexão SSH encerrada');
    } catch (error) {
      Logger.error('Erro ao encerrar SSH:', error);
    }
  }
}

// Executar os testes
testPingCountFix().catch(console.error);
