# Estrutura do Banco de Dados - Sem Fronteiras

## 📊 Visão Geral
Este documento descreve a estrutura do banco de dados PostgreSQL utilizado no sistema Sem Fronteiras. O banco de dados é gerenciado através do Prisma ORM e contém as principais entidades necessárias para o gerenciamento de servidores e execução de comandos SSH/RDP.

## 🏗️ Modelos

### User (Usuários)
Armazena informações dos usuários do sistema.

| Campo     | Tipo     | Descrição                                    | Constraints |
|-----------|----------|----------------------------------------------|-------------|
| id        | String   | Identificador único UUID                     | @id @default(uuid()) |
| email     | String   | Email do usuário                             | @unique |
| name      | String   | Nome completo do usuário                     | - |
| password  | String   | Senha criptografada (bcrypt)                 | - |
| role      | Role     | Nível de acesso (ADMIN/USER)                 | @default(USER) |
| createdAt | DateTime | Data de criação do registro                  | @default(now()) |
| updatedAt | DateTime | Data da última atualização                   | @updatedAt |

### Server (Servidores)
Gerencia os servidores remotos cadastrados.

| Campo      | Tipo     | Descrição                                    | Constraints |
|------------|----------|----------------------------------------------|-------------|
| id         | String   | Identificador único UUID                     | @id @default(uuid()) |
| name       | String   | Nome do servidor                             | - |
| host       | String   | Endereço IP ou hostname                      | - |
| port       | Int      | Porta de conexão                             | @default(22) |
| username   | String   | Usuário para autenticação                    | - |
| password   | String?  | Senha (opcional)                             | nullable |
| privateKey | String?  | Chave privada SSH (opcional)                 | nullable |
| os         | OS       | Sistema operacional (LINUX/WINDOWS)          | @default(LINUX) |
| userId     | String   | ID do usuário proprietário                   | foreign key |
| createdAt  | DateTime | Data de criação do registro                  | @default(now()) |
| updatedAt  | DateTime | Data da última atualização                   | @updatedAt |

### Command (Comandos)
Registra os comandos executados nos servidores.

| Campo       | Tipo     | Descrição                                    | Constraints |
|-------------|----------|----------------------------------------------|-------------|
| id          | String   | Identificador único UUID                     | @id @default(uuid()) |
| name        | String   | Nome/descrição do comando                    | - |
| command     | String   | Comando executado                            | - |
| description | String?  | Descrição detalhada do comando               | nullable |
| serverId    | String   | ID do servidor onde foi executado            | foreign key |
| createdAt   | DateTime | Data de criação do registro                  | @default(now()) |
| updatedAt   | DateTime | Data da última atualização                   | @updatedAt |

### CommandHistory (Histórico de Comandos)
Registra o histórico de execução de comandos nos servidores.

| Campo       | Tipo     | Descrição                                    | Constraints |
|-------------|----------|----------------------------------------------|-------------|
| id          | String   | Identificador único UUID                     | @id @default(uuid()) |
| userId      | String   | ID do usuário que executou o comando         | foreign key |
| serverId    | String   | ID do servidor onde foi executado            | foreign key |
| commandId   | String   | ID do comando executado                      | foreign key |
| result      | String?  | Resultado da execução (JSON)                 | nullable |
| status      | Int      | Código de status da execução                 | @default(0) |
| executedAt  | DateTime | Data e hora da execução                      | @default(now()) |

## 🔑 Enums

### Role (Níveis de Acesso)
```prisma
enum Role {
  ADMIN  // Acesso total ao sistema
  USER   // Acesso limitado
}
```

### OS (Sistemas Operacionais)
```prisma
enum OS {
  LINUX    // Sistema Linux
  WINDOWS  // Sistema Windows
}
```

## 🔗 Relacionamentos

1. User -> Server: One-to-Many
   - Um usuário pode ter múltiplos servidores
   - Cada servidor pertence a um único usuário

2. Server -> Command: One-to-Many
   - Um servidor pode ter múltiplos comandos
   - Cada comando está associado a um único servidor

3. User -> CommandHistory: One-to-Many
   - Um usuário pode ter múltiplos registros de histórico
   - Cada registro de histórico pertence a um único usuário

4. Server -> CommandHistory: One-to-Many
   - Um servidor pode ter múltiplos registros de histórico
   - Cada registro de histórico está associado a um único servidor

5. Command -> CommandHistory: One-to-Many
   - Um comando pode ter múltiplos registros de histórico
   - Cada registro de histórico está associado a um único comando

## 🔒 Segurança

- Senhas são armazenadas utilizando criptografia bcrypt
- Chaves privadas SSH são armazenadas de forma segura
- Credenciais sensíveis são criptografadas antes do armazenamento

## 📝 Notas de Implementação

1. **Índices**
   - Índice único em User.email
   - Índices em todas as chaves estrangeiras (userId, serverId)

2. **Soft Delete**
   - Implementação futura para não remover registros permanentemente

3. **Auditoria**
   - Todas as tabelas possuem campos de createdAt e updatedAt
   - Implementação futura de log de alterações

4. **Validações**
   - Validações de dados implementadas no nível do Prisma
   - Validações adicionais na camada de aplicação

## 🔄 Migrations

As migrations são gerenciadas pelo Prisma e estão localizadas em `./prisma/migrations/`.
Para aplicar as migrations:

```bash
npx prisma migrate deploy
```

Para criar uma nova migration:

```bash
npx prisma migrate dev --name nome_da_migration
``` 