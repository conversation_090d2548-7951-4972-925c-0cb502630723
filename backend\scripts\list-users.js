// Script para listar todos os usuários do sistema
// Uso: node scripts/list-users.js [--all]
// Opção --all: Inclui usuários inativos na listagem

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Função para analisar os argumentos da linha de comando
function parseArgs() {
  const args = process.argv.slice(2);
  return {
    includeInactive: args.includes('--all')
  };
}

// Função principal
async function listUsers() {
  try {
    console.log('=== LISTA DE USUÁRIOS DO SISTEMA ===');
    
    // Obter parâmetros da linha de comando
    const params = parseArgs();
    
    // Definir filtro
    const filter = params.includeInactive ? {} : { where: { active: true } };
    
    // Buscar usuários
    const users = await prisma.user.findMany({
      ...filter,
      orderBy: { name: 'asc' }
    });
    
    // Exibir contagem
    console.log(`Total de usuários: ${users.length}`);
    
    // Exibir usuários em formato tabular
    console.log('\nID                                   | Nome                  | Email                 | Função  | Status');
    console.log('--------------------------------------|------------------------|------------------------|---------|--------');
    
    users.forEach(user => {
      const id = user.id.padEnd(36);
      const name = user.name.substring(0, 20).padEnd(22);
      const email = user.email.substring(0, 20).padEnd(22);
      const role = (user.role === 'ADMIN' ? 'Admin' : 'Usuário').padEnd(7);
      const status = user.active ? 'Ativo' : 'Inativo';
      
      console.log(`${id} | ${name} | ${email} | ${role} | ${status}`);
    });
    
    console.log('\nUso: node scripts/list-users.js [--all]');
    console.log('Opção --all: Inclui usuários inativos na listagem');
    
  } catch (error) {
    console.error('Erro ao listar usuários:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    process.exit(0);
  }
}

// Executar a função principal
listUsers();
