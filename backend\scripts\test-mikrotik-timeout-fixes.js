const { NodeSSH } = require('node-ssh');
const { MikrotikExecutor } = require('../dist/services/ssh/executors/mikrotikExecutor');
const { Logger } = require('../dist/utils/logger');

async function testMikrotikTimeoutFixes() {
  const ssh = new NodeSSH();
  
  try {
    // Conectar ao Mikrotik
    Logger.log('🔌 Conectando ao Mikrotik...');
    await ssh.connect({
      host: '***********',
      username: 'admin',
      password: '88701181Sem*',
      port: 22,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1'],
        compress: ['none']
      }
    });
    
    Logger.log('✅ Conectado com sucesso!');
    
    // Criar executor
    const executor = new MikrotikExecutor(ssh);
    
    // Testes com timeouts estendidos
    const tests = [
      {
        name: 'Ping com count=5 - Teste timeout estendido',
        command: '/ping address=******* count=5',
        expectedTimeout: 60000,
        description: 'Verifica se ping completa com timeout de 60s'
      },
      {
        name: 'Ping com count=10 - Teste saída completa',
        command: '/ping address=******* count=10',
        expectedTimeout: 60000,
        description: 'Verifica se ping com mais pacotes retorna saída completa'
      },
      {
        name: 'Traceroute Google DNS - Teste timeout estendido',
        command: '/tool traceroute address=*******',
        expectedTimeout: 120000,
        description: 'Verifica se traceroute completa com timeout de 2 minutos'
      },
      {
        name: 'Traceroute Cloudflare - Teste detecção de fim',
        command: '/tool traceroute address=*******',
        expectedTimeout: 120000,
        description: 'Verifica se traceroute detecta fim corretamente'
      }
    ];
    
    for (const test of tests) {
      Logger.log(`\n🎯 === ${test.name} ===`);
      Logger.log(`📝 Comando: ${test.command}`);
      Logger.log(`⏱️ Timeout esperado: ${test.expectedTimeout}ms`);
      Logger.log(`📋 Descrição: ${test.description}`);
      
      try {
        const startTime = Date.now();
        
        Logger.log('🚀 Executando comando...');
        const result = await executor.executeCommand(test.command);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        Logger.log(`⏱️ Duração real: ${duration}ms`);
        Logger.log(`📊 Código de saída: ${result.code}`);
        
        if (result.stdout) {
          Logger.log('\n📤 === SAÍDA COMPLETA ===');
          console.log(result.stdout);
          Logger.log('=== FIM DA SAÍDA ===\n');
          
          // Análise específica por tipo de comando
          if (test.command.includes('ping')) {
            const hasStatistics = result.stdout.includes('sent=') && 
                                 result.stdout.includes('received=');
            const hasPacketData = result.stdout.includes('SEQ HOST');
            
            Logger.log('🔍 Análise do PING:');
            Logger.log(`  ${hasPacketData ? '✅' : '❌'} Dados de pacotes: ${hasPacketData ? 'ENCONTRADOS' : 'NÃO ENCONTRADOS'}`);
            Logger.log(`  ${hasStatistics ? '✅' : '❌'} Estatísticas finais: ${hasStatistics ? 'ENCONTRADAS' : 'NÃO ENCONTRADAS'}`);
            
            if (hasStatistics && hasPacketData) {
              Logger.log('🎉 PING COMPLETO - Teste PASSOU');
            } else {
              Logger.log('⚠️ PING INCOMPLETO - Teste FALHOU');
            }
          } else if (test.command.includes('traceroute')) {
            const hasHops = /\d+\s+\d+\.\d+\.\d+\.\d+/.test(result.stdout);
            const hasCompletion = result.stdout.toLowerCase().includes('trace complete') ||
                                 result.stdout.toLowerCase().includes('destination reached') ||
                                 /\d+\s+\*\s+\*\s+\*/.test(result.stdout);
            
            Logger.log('🔍 Análise do TRACEROUTE:');
            Logger.log(`  ${hasHops ? '✅' : '❌'} Dados de hops: ${hasHops ? 'ENCONTRADOS' : 'NÃO ENCONTRADOS'}`);
            Logger.log(`  ${hasCompletion ? '✅' : '❌'} Indicação de conclusão: ${hasCompletion ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
            
            if (hasHops) {
              Logger.log('🎉 TRACEROUTE COM DADOS - Teste PASSOU');
            } else {
              Logger.log('⚠️ TRACEROUTE SEM DADOS - Teste FALHOU');
            }
          }
        } else {
          Logger.log('❌ Nenhuma saída retornada');
        }
        
        if (result.stderr) {
          Logger.log('\n⚠️ === STDERR ===');
          console.log(result.stderr);
          Logger.log('=== FIM STDERR ===\n');
        }
        
        // Verificar se não houve timeout
        if (duration < test.expectedTimeout * 0.9) {
          Logger.log('✅ Comando completou dentro do tempo esperado');
        } else {
          Logger.log('⚠️ Comando demorou mais que o esperado (possível timeout)');
        }
        
      } catch (error) {
        Logger.error(`❌ Erro no teste: ${error.message}`);
        
        if (error.message.includes('timeout') || error.message.includes('Timeout')) {
          Logger.error('🕐 TIMEOUT DETECTADO - As correções podem não estar funcionando');
        }
      }
      
      // Pausa entre testes
      Logger.log('⏸️ Aguardando 5 segundos antes do próximo teste...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    Logger.log('\n🏁 === RESUMO DOS TESTES ===');
    Logger.log('✅ Testes de timeout e detecção de fim concluídos');
    Logger.log('📊 Verifique os logs acima para resultados detalhados');
    
  } catch (error) {
    Logger.error('❌ Erro durante os testes:', error);
  } finally {
    try {
      await ssh.dispose();
      Logger.log('🔌 Conexão SSH encerrada');
    } catch (error) {
      Logger.error('Erro ao encerrar SSH:', error);
    }
  }
}

// Executar os testes
testMikrotikTimeoutFixes().catch(console.error);
