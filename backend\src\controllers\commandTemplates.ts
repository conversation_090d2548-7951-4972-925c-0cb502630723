import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient, Role } from '@prisma/client'

const prisma = new PrismaClient()

// Listar templates de comandos
export async function listCommandTemplates(
  request: FastifyRequest,
  reply: FastifyReply,
) {
  try {
    // Buscar templates públicos e templates do usuário
    const templates = await prisma.commandTemplate.findMany({
      where: {
        OR: [
          { isPublic: true },
          { userId: request.user.id },
        ],
      },
      include: {
        commands: {
          orderBy: {
            order: 'asc',
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    })

    return reply.send({ templates })
  } catch (error) {
    console.error('Erro ao listar templates de comandos:', error)
    return reply.status(500).send({ error: 'Erro ao listar templates de comandos' })
  }
}

// Obter template por ID
export async function getCommandTemplate(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    const template = await prisma.commandTemplate.findUnique({
      where: { id },
      include: {
        commands: {
          orderBy: {
            order: 'asc',
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    if (!template) {
      return reply.status(404).send({ error: 'Template não encontrado' })
    }

    // Verificar se o template é público ou pertence ao usuário
    if (!template.isPublic && template.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Acesso negado' })
    }

    return reply.send(template)
  } catch (error) {
    console.error('Erro ao obter template de comandos:', error)
    return reply.status(500).send({ error: 'Erro ao obter template de comandos' })
  }
}

// Criar template de comandos
export async function createCommandTemplate(
  request: FastifyRequest<{
    Body: {
      name: string
      description?: string
      isPublic: boolean
      commands: {
        name: string
        command: string
        description?: string
        order: number
      }[]
    }
  }>,
  reply: FastifyReply,
) {
  try {
    const { name, description, isPublic, commands } = request.body

    // Validar se há pelo menos um comando
    if (!commands || commands.length === 0) {
      return reply.status(400).send({ error: 'O template deve conter pelo menos um comando' })
    }

    // Validar se todos os comandos têm nome e comando
    const invalidCommands = commands.filter(cmd => !cmd.name || !cmd.command)
    if (invalidCommands.length > 0) {
      return reply.status(400).send({ error: 'Todos os comandos devem ter nome e comando' })
    }

    const template = await prisma.commandTemplate.create({
      data: {
        name,
        description,
        isPublic,
        userId: request.user.id,
        commands: {
          create: commands.map(cmd => ({
            name: cmd.name,
            command: cmd.command,
            description: cmd.description,
            order: cmd.order,
          })),
        },
      },
      include: {
        commands: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    })

    return reply.status(201).send(template)
  } catch (error) {
    console.error('Erro ao criar template de comandos:', error)
    return reply.status(500).send({ error: 'Erro ao criar template de comandos' })
  }
}

// Atualizar template de comandos
export async function updateCommandTemplate(
  request: FastifyRequest<{
    Params: { id: string }
    Body: {
      name: string
      description?: string
      isPublic: boolean
      commands: {
        id?: string
        name: string
        command: string
        description?: string
        order: number
      }[]
    }
  }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params
    const { name, description, isPublic, commands } = request.body

    // Validar se há pelo menos um comando
    if (!commands || commands.length === 0) {
      return reply.status(400).send({ error: 'O template deve conter pelo menos um comando' })
    }

    // Validar se todos os comandos têm nome e comando
    const invalidCommands = commands.filter(cmd => !cmd.name || !cmd.command)
    if (invalidCommands.length > 0) {
      return reply.status(400).send({ error: 'Todos os comandos devem ter nome e comando' })
    }

    // Verificar se o template existe
    const template = await prisma.commandTemplate.findUnique({
      where: { id },
    })

    if (!template) {
      return reply.status(404).send({ error: 'Template não encontrado' })
    }

    // Verificar se o usuário é o proprietário do template ou um administrador
    if (template.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Acesso negado. Apenas o proprietário ou administradores podem editar o template.' })
    }

    // Atualizar template e seus comandos
    const updatedTemplate = await prisma.commandTemplate.update({
      where: { id },
      data: {
        name,
        description,
        isPublic,
        commands: {
          deleteMany: {},
          create: commands.map(cmd => ({
            name: cmd.name,
            command: cmd.command,
            description: cmd.description,
            order: cmd.order,
          })),
        },
      },
      include: {
        commands: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    })

    return reply.send(updatedTemplate)
  } catch (error) {
    console.error('Erro ao atualizar template de comandos:', error)
    return reply.status(500).send({ error: 'Erro ao atualizar template de comandos' })
  }
}

// Excluir template de comandos
export async function deleteCommandTemplate(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    // Verificar se o template existe
    const template = await prisma.commandTemplate.findUnique({
      where: { id },
    })

    if (!template) {
      return reply.status(404).send({ error: 'Template não encontrado' })
    }

    // Verificar se o usuário é o proprietário do template ou um administrador
    if (template.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Acesso negado. Apenas o proprietário ou administradores podem excluir o template.' })
    }

    // Excluir o template (os comandos serão excluídos automaticamente devido à relação onDelete: Cascade)
    await prisma.commandTemplate.delete({
      where: { id },
    })

    return reply.send({ message: 'Template excluído com sucesso' })
  } catch (error) {
    console.error('Erro ao excluir template de comandos:', error)
    return reply.status(500).send({ error: 'Erro ao excluir template de comandos' })
  }
}

// Aplicar template a um servidor
export async function applyTemplateToServer(
  request: FastifyRequest<{
    Params: { id: string, serverId: string }
  }>,
  reply: FastifyReply,
) {
  try {
    const { id, serverId } = request.params

    // Verificar se o template existe
    const template = await prisma.commandTemplate.findUnique({
      where: { id },
      include: {
        commands: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    })

    if (!template) {
      return reply.status(404).send({ error: 'Template não encontrado' })
    }

    // Verificar se o template é público ou pertence ao usuário
    if (!template.isPublic && template.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Acesso negado' })
    }

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id: serverId },
      include: {
        commands: true,
      },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário é o proprietário do servidor ou tem acesso a ele
    const isOwner = server.userId === request.user.id
    const isAdmin = request.user.role === Role.ADMIN

    if (!isOwner && !isAdmin) {
      // Verificar se o usuário tem acesso explícito ao servidor
      const hasAccess = await prisma.serverUser.findFirst({
        where: {
          serverId,
          userId: request.user.id,
        },
      })

      if (!hasAccess) {
        return reply.status(403).send({ error: 'Acesso negado ao servidor' })
      }
    }

    // Adicionar comandos do template ao servidor
    const updatedServer = await prisma.server.update({
      where: { id: serverId },
      data: {
        commands: {
          create: template.commands.map(cmd => ({
            name: cmd.name,
            command: cmd.command,
            description: cmd.description,
          })),
        },
      },
      include: {
        commands: true,
      },
    })

    return reply.send({
      message: 'Template aplicado com sucesso',
      server: updatedServer,
    })
  } catch (error) {
    console.error('Erro ao aplicar template ao servidor:', error)
    return reply.status(500).send({ error: 'Erro ao aplicar template ao servidor' })
  }
} 