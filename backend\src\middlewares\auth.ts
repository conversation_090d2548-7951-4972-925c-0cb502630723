import { FastifyRequest, FastifyReply } from 'fastify'
import jwt from 'jsonwebtoken'
import { Role } from '@prisma/client'
import { config } from '../config/auth'

declare module 'fastify' {
  interface FastifyRequest {
    user: {
      id: string
      role: Role
    }
  }
}

export async function verifyJWT(request: FastifyRequest, reply: FastifyReply) {
  try {
    const authHeader = request.headers.authorization

    if (!authHeader) {
      return reply.status(401).send({ error: 'Token não fornecido' })
    }

    const [, token] = authHeader.split(' ')

    const decoded = jwt.verify(token, config.jwt.secret) as {
      id: string
      role: Role
    }

    request.user = {
      id: decoded.id,
      role: decoded.role
    }
  } catch (error) {
    return reply.status(401).send({ error: 'Token inválido' })
  }
} 