/**
 * Script para testar a conexão SSH com dispositivos Mikrotik
 * 
 * Este script testa a execução de comandos em um dispositivo Mikrotik
 * usando a implementação atualizada do MikrotikExecutor.
 * 
 * Uso: node test-mikrotik.js <host> <porta> <usuário> <senha> <comando>
 */

// Importar dependências
const { NodeSSH } = require('node-ssh');
const path = require('path');
const fs = require('fs');

// Configurar logger simples
const Logger = {
  log: (...args) => console.log(`INFO [${new Date().toLocaleTimeString()}]:`, ...args),
  error: (...args) => console.error(`ERROR [${new Date().toLocaleTimeString()}]:`, ...args),
  warn: (...args) => console.warn(`WARN [${new Date().toLocaleTimeString()}]:`, ...args)
};

// Verificar argumentos
const args = process.argv.slice(2);
if (args.length < 5) {
  console.error('Uso: node test-mikrotik.js <host> <porta> <usuário> <senha> <comando>');
  process.exit(1);
}

const [host, port, username, password, command] = args;

// Função principal
async function main() {
  const ssh = new NodeSSH();
  let shell = null;

  try {
    Logger.log(`Conectando a ${host}:${port} como ${username}...`);
    
    // Conectar ao servidor SSH
    await ssh.connect({
      host,
      port: parseInt(port, 10),
      username,
      password,
      readyTimeout: 20000,
      keepaliveInterval: 5000,
      keepaliveCountMax: 3,
      algorithms: {
        kex: [
          'diffie-hellman-group-exchange-sha256',
          'diffie-hellman-group14-sha256',
          'diffie-hellman-group16-sha512',
          'diffie-hellman-group18-sha512',
          'diffie-hellman-group-exchange-sha1',
          'diffie-hellman-group14-sha1',
          'diffie-hellman-group1-sha1'
        ],
        cipher: [
          'aes128-ctr',
          'aes192-ctr',
          'aes256-ctr',
          'aes128-gcm',
          '<EMAIL>',
          'aes256-gcm',
          '<EMAIL>'
        ],
        serverHostKey: [
          'ssh-rsa',
          'ssh-dss',
          'ecdsa-sha2-nistp256',
          'ecdsa-sha2-nistp384',
          'ecdsa-sha2-nistp521'
        ]
      }
    });

    Logger.log('Conexão estabelecida com sucesso!');
    Logger.log(`Executando comando: ${command}`);

    // Iniciar shell interativo
    shell = await ssh.requestShell({
      term: 'dumb',
      rows: 24,
      cols: 120,
      wrap: 120,
      ptyType: 'vanilla'
    });

    let output = '';
    let lastDataTime = Date.now();
    let commandCompleted = false;

    // Configurar handlers para eventos
    shell.on('data', (data) => {
      const chunk = data.toString('utf8');
      output += chunk;
      lastDataTime = Date.now();
      Logger.log(`Recebido (${chunk.length} bytes): ${chunk}`);

      // Verificar se detectamos o prompt do Mikrotik
      if (chunk.includes('[admin@MikroTik]') ||
          chunk.includes('>') ||
          chunk.includes('More:')) {
        
        // Se encontrar o prompt "More:", enviar espaço para continuar
        if (chunk.includes('More:')) {
          shell.write(' ');
          Logger.log('Detectado prompt "More:", enviando espaço para continuar');
        }
      }
    });

    shell.stderr?.on('data', (data) => {
      const chunk = data.toString('utf8');
      Logger.error(`Erro: ${chunk}`);
    });

    shell.on('error', (err) => {
      Logger.error('Erro no shell:', err);
    });

    shell.on('close', () => {
      Logger.log('Shell fechado');
      if (!commandCompleted) {
        commandCompleted = true;
        Logger.log('Resultado final:');
        Logger.log(output);
        cleanup();
      }
    });

    // Aguardar um momento para o shell inicializar
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Enviar um Enter inicial para limpar qualquer prompt pendente
    shell.write('\n');
    
    // Aguardar um momento antes de enviar o comando
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Enviar o comando
    shell.write(command + '\n');
    
    // Aguardar a conclusão do comando (timeout de 30 segundos)
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Mostrar resultado final
    Logger.log('Resultado final após 30 segundos:');
    Logger.log(output);
    
    // Limpar recursos
    cleanup();
  } catch (error) {
    Logger.error('Erro:', error);
    cleanup();
    process.exit(1);
  }

  function cleanup() {
    try {
      if (shell && !shell.ended) {
        // Enviar comando quit para encerrar a sessão corretamente
        shell.write('/quit\n');
        
        // Pequena pausa antes de encerrar o shell
        setTimeout(() => {
          try {
            shell.end();
          } catch (e) {
            Logger.error('Erro ao fechar shell após quit:', e);
          }
          
          // Desconectar SSH
          ssh.dispose();
          process.exit(0);
        }, 1000);
      } else {
        // Desconectar SSH
        ssh.dispose();
        process.exit(0);
      }
    } catch (e) {
      Logger.error('Erro ao limpar recursos:', e);
      process.exit(1);
    }
  }
}

// Executar script
main().catch(error => {
  Logger.error('Erro não tratado:', error);
  process.exit(1);
});
