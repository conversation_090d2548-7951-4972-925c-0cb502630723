{"name": "sem-fronteiras-backend", "version": "1.0.0", "description": "Backend do sistema Sem Fronteiras", "main": "src/server.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:seed": "ts-node prisma/seed.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^8.0.0", "@fastify/jwt": "^7.0.0", "@prisma/client": "^5.0.0", "@types/react-beautiful-dnd": "^13.1.8", "bcryptjs": "^2.4.3", "dotenv": "^16.0.0", "fastify": "^4.0.0", "jsonwebtoken": "^9.0.0", "node-routeros-v2": "^1.6.12", "node-ssh": "^13.0.0", "pino-pretty": "^13.0.0", "react-beautiful-dnd": "^13.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0", "zod": "^3.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^18.19.79", "jest": "^29.0.0", "prisma": "^5.0.0", "ts-jest": "^29.0.0"}}