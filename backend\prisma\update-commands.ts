import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const updatedCommands = [
  {
    name: 'Listar arquivos',
    command: 'ls -la',
    description: 'Lista todos os arquivos e diretórios com detalhes'
  },
  {
    name: 'Mostrar diretório atual',
    command: 'pwd',
    description: 'Exibe o caminho completo do diretório atual'
  },
  {
    name: 'Informações do sistema',
    command: 'uname -a',
    description: 'Mostra informações detalhadas do sistema operacional'
  },
  {
    name: 'Uso do disco',
    command: 'df -h',
    description: 'Mostra o uso do disco em formato legível'
  },
  {
    name: 'Processos em execução',
    command: 'ps -eo pid,ppid,user,%cpu,%mem,stat,start,time,command --sort=-%cpu',
    description: 'Lista os processos em execução, ordenados por uso de CPU'
  },
  {
    name: '<PERSON>o de memória',
    command: 'free -h',
    description: 'Mostra informações detalhadas sobre o uso de memória'
  },
  {
    name: 'Carga do sistema',
    command: 'uptime',
    description: 'Mostra o tempo de atividade e carga média do sistema'
  }
]

async function main() {
  try {
    // Buscar todos os servidores
    const servers = await prisma.server.findMany()

    for (const server of servers) {
      // Buscar comandos existentes para o servidor
      const existingCommands = await prisma.command.findMany({
        where: { serverId: server.id }
      })

      // Verificar quais comandos padrão estão faltando
      const existingCommandNames = existingCommands.map(cmd => cmd.name)
      const missingCommands = updatedCommands.filter(cmd => !existingCommandNames.includes(cmd.name))

      // Adicionar apenas os comandos padrão que estão faltando
      if (missingCommands.length > 0) {
        await prisma.command.createMany({
          data: missingCommands.map(cmd => ({
            ...cmd,
            serverId: server.id
          })),
          skipDuplicates: true
        })
      }
    }

    console.log('Comandos atualizados com sucesso!')
  } catch (error) {
    console.error('Erro ao atualizar comandos:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()