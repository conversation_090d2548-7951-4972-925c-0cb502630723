/**
 * Script para buscar um servidor Mikrotik no banco de dados e executar um comando SSH
 * 
 * Este script:
 * 1. Busca um servidor com "Mikrotik" no nome
 * 2. Estabelece uma conexão SSH com esse servidor
 * 3. Executa o comando `/ip address print`
 * 4. Exibe o resultado
 * 
 * Uso: node scripts/mikrotik-command.js
 */

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const { NodeSSH } = require('node-ssh');
const fs = require('fs');
const path = require('path');

// Inicializar o cliente Prisma
const prisma = new PrismaClient();

// Configurações
const LOG_DIR = path.join(__dirname, '../logs');
const LOG_FILE = path.join(LOG_DIR, 'mikrotik-command.log');

// Garantir que o diretório de logs exista
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Função para registrar logs
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Função para buscar servidor Mikrotik
async function findMikrotikServer() {
  try {
    log('Buscando servidor Mikrotik no banco de dados...');
    
    // Buscar servidor com "Mikrotik" no nome (case insensitive)
    const server = await prisma.server.findFirst({
      where: {
        name: {
          contains: 'Mikrotik',
          mode: 'insensitive'
        }
      },
      include: {
        commands: true
      }
    });
    
    if (!server) {
      log('ERRO: Nenhum servidor Mikrotik encontrado no banco de dados.');
      return null;
    }
    
    log(`Servidor Mikrotik encontrado: ${server.name} (${server.host}:${server.port})`);
    return server;
  } catch (error) {
    log(`ERRO ao buscar servidor Mikrotik: ${error.message}`);
    return null;
  }
}

// Função para executar comando SSH
async function executeSSHCommand(server) {
  const ssh = new NodeSSH();
  
  try {
    log(`Conectando ao servidor ${server.name} (${server.host}:${server.port})...`);
    
    // Configurar opções de conexão
    const sshConfig = {
      host: server.host,
      port: server.port,
      username: server.username,
      password: server.password,
      privateKey: server.privateKey,
      readyTimeout: 30000, // 30 segundos
      keepaliveInterval: 10000, // 10 segundos
      keepaliveCountMax: 3,
      algorithms: {
        kex: [
          'diffie-hellman-group-exchange-sha256',
          'diffie-hellman-group14-sha256',
          'diffie-hellman-group16-sha512',
          'diffie-hellman-group18-sha512',
          'diffie-hellman-group-exchange-sha1',
          'diffie-hellman-group14-sha1',
          'diffie-hellman-group1-sha1'
        ],
        cipher: [
          'aes128-ctr',
          'aes192-ctr',
          'aes256-ctr',
          'aes128-gcm',
          '<EMAIL>',
          'aes256-gcm',
          '<EMAIL>'
        ],
        serverHostKey: [
          'ssh-rsa',
          'ssh-dss',
          'ecdsa-sha2-nistp256',
          'ecdsa-sha2-nistp384',
          'ecdsa-sha2-nistp521'
        ]
      }
    };
    
    // Remover opções indefinidas
    Object.keys(sshConfig).forEach(key => {
      if (sshConfig[key] === undefined || sshConfig[key] === null) {
        delete sshConfig[key];
      }
    });
    
    // Conectar ao servidor
    await ssh.connect(sshConfig);
    log('Conexão SSH estabelecida com sucesso!');
    
    // Comando a ser executado
    const command = '/ip address print';
    log(`Executando comando: ${command}`);
    
    // Executar o comando
    const result = await ssh.execCommand(command);
    
    // Exibir resultado
    log('Comando executado com sucesso!');
    log('--- SAÍDA DO COMANDO ---');
    log(result.stdout || '(sem saída)');
    
    if (result.stderr) {
      log('--- ERROS ---');
      log(result.stderr);
    }
    
    // Fechar conexão
    ssh.dispose();
    log('Conexão SSH encerrada.');
    
    return result;
  } catch (error) {
    log(`ERRO na execução do comando SSH: ${error.message}`);
    
    // Garantir que a conexão seja fechada em caso de erro
    try {
      ssh.dispose();
    } catch (e) {
      // Ignorar erros ao fechar conexão
    }
    
    return { stdout: '', stderr: error.message };
  }
}

// Função principal
async function main() {
  try {
    log('Iniciando script de comando Mikrotik...');
    
    // Buscar servidor Mikrotik
    const server = await findMikrotikServer();
    if (!server) {
      process.exit(1);
    }
    
    // Executar comando SSH
    await executeSSHCommand(server);
    
    log('Script concluído com sucesso!');
  } catch (error) {
    log(`ERRO: ${error.message}`);
  } finally {
    // Desconectar do banco de dados
    await prisma.$disconnect();
  }
}

// Executar função principal
main();
