import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Role } from '../types/user'
import { User, Mail, Lock, Shield } from 'lucide-react'

// Esquema base para todos os formulários
const baseUserFormSchema = {
  name: z.string().min(3, 'Nome deve ter no mínimo 3 caracteres'),
  email: z.string().email('E-mail inválido'),
  role: z.enum(['ADMIN', 'USER'] as const).optional()
}

// Esquema para criação (com senha obrigatória)
const createUserFormSchema = z.object({
  ...baseUserFormSchema,
  password: z.string().min(6, 'Senha deve ter no mínimo 6 caracteres')
})

// Esquema para edição (com senha realmente opcional)
const updateUserFormSchema = z.object({
  ...baseUserFormSchema,
  password: z.string().optional()
})

// Tipos derivados dos esquemas
type CreateUserFormData = z.infer<typeof createUserFormSchema>
type UpdateUserFormData = z.infer<typeof updateUserFormSchema>
export type UserFormData = CreateUserFormData | UpdateUserFormData

interface UserFormProps {
  onSubmit: (data: UserFormData) => void
  initialData?: Partial<UserFormData> | null
  onCancel?: () => void
  isEditing?: boolean
  isAdmin?: boolean
}

export function UserForm({ onSubmit, initialData, onCancel, isEditing = false, isAdmin = true }: UserFormProps) {
  // Escolhe o esquema apropriado com base no modo (criação ou edição)
  const formSchema = isEditing ? updateUserFormSchema : createUserFormSchema

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<UserFormData>({
    resolver: zodResolver(formSchema) as any, // Usamos any aqui para evitar problemas de tipagem
    defaultValues: initialData || undefined
  })

  const handleFormSubmit = (data: UserFormData) => {
    // Se estiver editando e a senha estiver vazia, remova o campo
    if (isEditing && (!data.password || data.password === '')) {
      const { password, ...restData } = data as UpdateUserFormData
      onSubmit(restData)
    } else {
      onSubmit(data)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="space-y-4">
        {/* Campo Nome */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Nome
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="name"
              {...register('name')}
              placeholder="Nome completo"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name?.message?.toString()}</p>
          )}
        </div>

        {/* Campo E-mail */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            E-mail
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="email"
              id="email"
              {...register('email')}
              placeholder="<EMAIL>"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email?.message?.toString()}</p>
          )}
        </div>

        {/* Campo Senha */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            {isEditing ? 'Senha (deixe em branco para manter a atual)' : 'Senha'}
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Lock className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="password"
              id="password"
              {...register('password')}
              placeholder={isEditing ? "••••••" : "Mínimo 6 caracteres"}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password?.message?.toString()}</p>
          )}
        </div>

        {/* Campo Nível de Acesso */}
        {isAdmin && (
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700">
              Nível de Acesso
            </label>
            <div className="mt-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Shield className="h-5 w-5 text-gray-400" />
              </div>
              <select
                id="role"
                {...register('role')}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="USER">Usuário</option>
                <option value="ADMIN">Administrador</option>
              </select>
            </div>
            {errors.role && (
              <p className="mt-1 text-sm text-red-600">{errors.role?.message?.toString()}</p>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancelar
          </button>
        )}
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isSubmitting ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
        </button>
      </div>
    </form>
  )
} 