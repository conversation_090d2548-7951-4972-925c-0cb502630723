import React from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { userService } from '../../services/userService'
import { UserForm } from '../../components/UserForm'
import { toast } from 'react-hot-toast'

export function ProfilePage() {
  const { data: profile, isLoading } = useQuery({
    queryKey: ['profile'],
    queryFn: userService.getProfile
  })

  const updateMutation = useMutation({
    mutationFn: (data: any) => userService.update(profile!.user.id, data),
    onSuccess: () => {
      toast.success('Perfil atualizado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao atualizar perfil')
    }
  })

  const handleUpdateProfile = (data: any) => {
    updateMutation.mutate(data)
  }

  if (isLoading) {
    return <div>Carregando...</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Meu Perfil</h1>
        
        <div className="bg-white shadow-md rounded-lg p-6">
          <UserForm
            onSubmit={handleUpdateProfile}
            initialData={profile?.user}
            isEditing={true}
            isAdmin={false}
          />
        </div>
      </div>
    </div>
  )
} 