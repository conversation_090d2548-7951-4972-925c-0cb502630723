#!/bin/bash

# Script para configurar o monitoramento de sessões SSH
# Este script deve ser executado durante a inicialização do servidor

# Detectar ambiente
if [ -d "/var/www/sem-fronteiras-ssh" ]; then
  echo "Executando em ambiente de PRODUÇÃO"
  AMBIENTE="produção"
  # Configurações de produção
  PROJETO_DIR="/var/www/sem-fronteiras-ssh"
else
  echo "Executando em ambiente de DESENVOLVIMENTO"
  AMBIENTE="desenvolvimento"
  # Configurações de desenvolvimento
  PROJETO_DIR="."
fi

# Criar diretório de logs se não existir
mkdir -p "$PROJETO_DIR/backend/logs"
chmod 755 "$PROJETO_DIR/backend/logs"

# Configurar cron para executar o script de monitoramento a cada 5 minutos
if [ "$AMBIENTE" = "produção" ]; then
  echo "Configurando cron para monitoramento de sessões SSH..."
  
  # Verificar se o crontab já contém a entrada
  CRON_ENTRY="*/5 * * * * node $PROJETO_DIR/backend/scripts/monitor-ssh.js >> $PROJETO_DIR/backend/logs/ssh-monitor.log 2>&1"
  CRON_CHECK=$(crontab -l 2>/dev/null | grep -F "$PROJETO_DIR/backend/scripts/monitor-ssh.js")
  
  if [ -z "$CRON_CHECK" ]; then
    # Adicionar a entrada ao crontab
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
    echo "Monitoramento de sessões SSH configurado com sucesso."
  else
    echo "Monitoramento de sessões SSH já está configurado."
  fi
else
  echo "Em ambiente de desenvolvimento, o monitoramento deve ser executado manualmente."
  echo "Para executar o monitoramento: node backend/scripts/monitor-ssh.js"
fi

# Executar o script de monitoramento uma vez para verificar sessões pendentes
echo "Executando verificação inicial de sessões SSH..."
node "$PROJETO_DIR/backend/scripts/monitor-ssh.js"

echo "Configuração de monitoramento SSH concluída."
