import { NodeSSH } from 'node-ssh';
import { SSHServer } from '../../types/server';
import { SSHConfig } from '../../types/sshConfig';
import { SSHError } from '../../types/sshError';
import { Logger } from '../../utils/logger';
import { Device, detectDevice } from './deviceDetector';

/**
 * Gerencia conexões SSH, incluindo conexão, reconexão e desconexão
 */
export class ConnectionManager {
  private ssh: NodeSSH;
  private isConnected: boolean = false;
  private connectionAttempts: number = 0;
  private maxConnectionAttempts: number = 3;
  private lastServer?: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>;
  private reconnectTimer?: NodeJS.Timeout;
  private connectionLock: boolean = false;
  private forceDisconnectTimer?: NodeJS.Timeout;
  private deviceType: Device = Device.GENERIC;

  constructor() {
    this.ssh = new NodeSSH();

    // Configurar handler global para erros não tratados
    process.on('uncaughtException', (error) => {
      Logger.error('Erro não tratado capturado pelo ConnectionManager:', error);
      this.handleConnectionError(error);
    });

    // Configurar handler para desconexão ao encerrar o processo
    process.on('SIGINT', () => {
      Logger.log('Processo interrompido, desconectando sessões SSH...');
      this.forceDisconnect();
    });

    process.on('SIGTERM', () => {
      Logger.log('Processo terminado, desconectando sessões SSH...');
      this.forceDisconnect();
    });
  }

  /**
   * Conecta ao servidor SSH
   * @param server Informações do servidor
   */
  async connect(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<void> {
    // Verificar se já existe uma conexão em andamento
    if (this.connectionLock) {
      Logger.log('Conexão SSH já em andamento, aguardando...');
      // Aguardar até que a conexão atual seja concluída ou falhe
      await new Promise<void>((resolve) => {
        const checkLock = () => {
          if (!this.connectionLock) {
            resolve();
          } else {
            setTimeout(checkLock, 500);
          }
        };
        checkLock();
      });
    }

    // Adquirir o bloqueio de conexão
    this.connectionLock = true;

    try {
      // Verificar se já existe uma conexão ativa e encerrá-la
      if (this.isConnected) {
        Logger.log('Conexão SSH já existente, desconectando antes de criar uma nova...');
        await this.disconnect();
      }

      // Limpar qualquer tentativa de reconexão pendente
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = undefined;
      }

      // Armazenar informações do servidor para possível reconexão
      this.lastServer = server;
      this.connectionAttempts++;

      // Detectar o tipo de dispositivo
      this.deviceType = detectDevice(server.name);

      Logger.log(`Iniciando conexão SSH com ${server.host}:${server.port} (${server.name}) - Tentativa ${this.connectionAttempts}`);

      // Criar configuração base
      const config: SSHConfig = this.createSSHConfig(server);

      await this.ssh.connect(config);
      Logger.log(`Conexão SSH estabelecida com sucesso: ${server.host}`);

      // Resetar contador de tentativas após conexão bem-sucedida
      this.connectionAttempts = 0;
      this.isConnected = true;

      // Configurar handlers para eventos de conexão
      this.setupConnectionHandlers();

      // Configurar um timer para forçar a desconexão após um período máximo
      // Isso evita que sessões fiquem abertas indefinidamente
      this.setupForceDisconnectTimer();
    } catch (error) {
      Logger.error(`Erro ao conectar via SSH com ${server.host}:`, error);
      this.isConnected = false;

      // Tentar reconectar se não excedeu o número máximo de tentativas
      if (this.connectionAttempts < this.maxConnectionAttempts) {
        Logger.log(`Tentando reconectar em 5 segundos... (Tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
        return this.scheduleReconnect(5000);
      } else {
        this.connectionAttempts = 0;
        // Liberar o bloqueio de conexão antes de lançar o erro
        this.connectionLock = false;
        throw new Error(`Falha ao conectar via SSH após ${this.maxConnectionAttempts} tentativas: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    } finally {
      // Garantir que o bloqueio de conexão seja liberado
      this.connectionLock = false;
    }
  }

  /**
   * Cria a configuração SSH com base no tipo de dispositivo
   * @param server Informações do servidor
   * @returns Configuração SSH
   */
  private createSSHConfig(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): SSHConfig {
    // Configuração base
    const config: SSHConfig = {
      host: server.host,
      port: server.port,
      username: server.username,
      readyTimeout: 20000, // 20 segundos para timeout de conexão
      keepaliveInterval: 5000, // 5 segundos para keepalive
      keepaliveCountMax: 3, // Número máximo de pacotes keepalive sem resposta
      reconnect: false // Gerenciamos a reconexão manualmente
    };

    // Configurar autenticação
    if (server.privateKey) {
      config.privateKey = server.privateKey;
    } else if (server.password) {
      config.password = server.password;
    } else {
      throw new Error('Nenhum método de autenticação fornecido');
    }

    // Aplicar configurações específicas por tipo de dispositivo
    switch (this.deviceType) {
      case Device.HUAWEI:
        // Configurações mais conservadoras para Huawei
        config.readyTimeout = 30000; // 30 segundos para timeout de conexão
        config.keepaliveInterval = 10000; // 10 segundos para keepalive
        config.keepaliveCountMax = 5; // Mais pacotes keepalive sem resposta

        // Algoritmos compatíveis com Huawei
        config.algorithms = {
          kex: [
            'diffie-hellman-group-exchange-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group1-sha1'
          ],
          cipher: [
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr',
            'aes128-gcm',
            '<EMAIL>',
            'aes256-gcm',
            '<EMAIL>'
          ],
          serverHostKey: [
            'ssh-rsa',
            'ssh-dss',
            'ecdsa-sha2-nistp256',
            'ecdsa-sha2-nistp384',
            'ecdsa-sha2-nistp521'
          ]
        };
        break;

      case Device.MIKROTIK:
        // Configurações otimizadas para Mikrotik
        config.readyTimeout = 180000; // 180 segundos para timeout de conexão (aumentado)

        // Desativar keepalive automático da biblioteca SSH2
        // Isso evita o problema de "Keepalive timeout" causado pela forma como o Mikrotik responde
        config.keepaliveInterval = 0; // Desativar keepalive automático
        config.keepaliveCountMax = 0; // Desativar keepalive automático

        // Algoritmos compatíveis com Mikrotik
        config.algorithms = {
          kex: [
            'diffie-hellman-group-exchange-sha256',
            'diffie-hellman-group14-sha256',
            'diffie-hellman-group16-sha512',
            'diffie-hellman-group18-sha512',
            'diffie-hellman-group-exchange-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group1-sha1'
          ],
          cipher: [
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr',
            'aes128-gcm',
            '<EMAIL>',
            'aes256-gcm',
            '<EMAIL>'
          ],
          serverHostKey: [
            'ssh-rsa',
            'ssh-dss',
            'ecdsa-sha2-nistp256',
            'ecdsa-sha2-nistp384',
            'ecdsa-sha2-nistp521'
          ]
          // A propriedade hmac não é suportada pelo tipo SSHConfig
          // Removido para evitar erros de compilação
        };
        break;

      case Device.DMOS:
        // Configurações mais conservadoras para DmOS
        config.readyTimeout = 35000; // 35 segundos para timeout de conexão
        config.keepaliveInterval = 8000; // 8 segundos para keepalive
        config.keepaliveCountMax = 4; // Número de pacotes keepalive sem resposta
        break;
    }

    return config;
  }

  /**
   * Configura handlers para eventos de conexão
   */
  private setupConnectionHandlers(): void {
    // Configurar handlers para eventos de conexão
    this.ssh.connection?.on('error', (error: any) => {
      Logger.error('Erro na conexão SSH:', error);
      this.handleConnectionError(error);
    });

    this.ssh.connection?.on('close', () => {
      Logger.log('Conexão SSH fechada');
      this.isConnected = false;

      // Limpar o timer de desconexão forçada
      if (this.forceDisconnectTimer) {
        clearTimeout(this.forceDisconnectTimer);
        this.forceDisconnectTimer = undefined;
      }

      // Desabilitar reconexão automática para evitar acúmulo de sessões
      // Especialmente importante para dispositivos Nokia
      if (this.deviceType === Device.NOKIA) {
        Logger.log('Dispositivo Nokia: desabilitando reconexão automática');
        return;
      }

      // Tentar reconectar automaticamente apenas se não for Nokia
      if (this.lastServer && this.connectionAttempts < this.maxConnectionAttempts) {
        this.scheduleReconnect(5000);
      }
    });

    this.ssh.connection?.on('timeout', () => {
      Logger.log('Timeout na conexão SSH');
      this.isConnected = false;

      // Limpar o timer de desconexão forçada
      if (this.forceDisconnectTimer) {
        clearTimeout(this.forceDisconnectTimer);
        this.forceDisconnectTimer = undefined;
      }

      // Desabilitar reconexão automática para dispositivos Nokia
      if (this.deviceType === Device.NOKIA) {
        Logger.log('Dispositivo Nokia: desabilitando reconexão automática após timeout');
        return;
      }

      // Tentar reconectar automaticamente apenas se não for Nokia
      if (this.lastServer && this.connectionAttempts < this.maxConnectionAttempts) {
        this.scheduleReconnect(5000);
      }
    });
  }

  /**
   * Trata erros de conexão
   * @param error Erro de conexão
   */
  private handleConnectionError(error: SSHError | any): void {
    Logger.error('Tratando erro de conexão SSH:', error);
    this.isConnected = false;

    // Limpar o timer de desconexão forçada
    if (this.forceDisconnectTimer) {
      clearTimeout(this.forceDisconnectTimer);
      this.forceDisconnectTimer = undefined;
    }

    // Limpar recursos
    try {
      this.ssh.dispose();
    } catch (e) {
      Logger.error('Erro ao limpar recursos SSH:', e);
    }

    // Criar nova instância SSH
    this.ssh = new NodeSSH();

    // Desabilitar reconexão automática para dispositivos Nokia
    if (this.deviceType === Device.NOKIA) {
      Logger.log('Dispositivo Nokia: desabilitando reconexão automática após erro');
      return;
    }

    // Tentar reconectar se tiver informações do servidor e não for Nokia
    if (this.lastServer && this.connectionAttempts < this.maxConnectionAttempts) {
      this.scheduleReconnect(5000);
    }
  }

  /**
   * Agenda uma reconexão após um atraso
   * @param delay Atraso em milissegundos
   */
  private scheduleReconnect(delay: number): Promise<void> {
    return new Promise((resolve) => {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      this.reconnectTimer = setTimeout(async () => {
        if (this.lastServer) {
          try {
            await this.connect(this.lastServer);
            resolve();
          } catch (error) {
            Logger.error('Falha na tentativa de reconexão:', error);
            // Não resolvemos a promise em caso de falha para não dar falsa impressão de sucesso
          }
        }
      }, delay);
    });
  }

  /**
   * Configura um timer para forçar a desconexão após um período máximo
   */
  private setupForceDisconnectTimer(): void {
    // Limpar timer existente, se houver
    if (this.forceDisconnectTimer) {
      clearTimeout(this.forceDisconnectTimer);
    }

    // Definir um tempo máximo para a sessão
    const MAX_SESSION_TIME = 600000; // 10 minutos (aumentado de 2 para 10 minutos)

    this.forceDisconnectTimer = setTimeout(() => {
      Logger.log('Tempo máximo de sessão atingido, forçando desconexão...');
      this.forceDisconnect();
    }, MAX_SESSION_TIME);
  }

  /**
   * Força a desconexão e limpa todos os recursos
   */
  private forceDisconnect(): void {
    Logger.log('Forçando desconexão da sessão SSH...');

    // Limpar timers
    if (this.forceDisconnectTimer) {
      clearTimeout(this.forceDisconnectTimer);
      this.forceDisconnectTimer = undefined;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }

    // Desconectar e limpar recursos
    try {
      // Tratamento especial para dispositivos Nokia antes de desconectar
      if (this.deviceType === Device.NOKIA && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Dispositivo Nokia: desconectando sem enviar comando de logout');
          // Para Nokia, não enviar comando de logout para evitar o erro "Unable to exec"
          // Apenas desconectar diretamente
          this.ssh.dispose();
        } catch (e) {
          Logger.error('Erro ao desconectar Nokia:', e);
          // Garantir que a desconexão ocorra mesmo em caso de erro
          try {
            this.ssh.dispose();
          } catch (disposeError) {
            Logger.error('Erro secundário ao desconectar Nokia:', disposeError);
          }
        }
      }
      // Enviar comando de quit para dispositivos Mikrotik antes de desconectar
      else if (this.deviceType === Device.MIKROTIK && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Enviando comando de quit para dispositivo Mikrotik antes de desconectar');
          // Tentar enviar comando de quit, mas não aguardar resposta
          this.ssh.execCommand('quit', { cwd: '/' }).catch(e => {
            Logger.error('Erro ao enviar comando de quit:', e);
          });

          // Pequena pausa para permitir que o comando seja processado
          setTimeout(() => {
            this.ssh.dispose();
          }, 1000);
        } catch (e) {
          Logger.error('Erro ao enviar quit para Mikrotik:', e);
          this.ssh.dispose();
        }
      } else {
        this.ssh.dispose();
      }
    } catch (e) {
      Logger.error('Erro ao forçar desconexão SSH:', e);
    } finally {
      // Garantir que o estado seja resetado
      this.isConnected = false;
      this.connectionAttempts = 0;
      this.ssh = new NodeSSH();
      this.connectionLock = false;
    }
  }

  /**
   * Desconecta do servidor SSH
   */
  async disconnect(): Promise<void> {
    try {
      // Limpar qualquer tentativa de reconexão pendente
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = undefined;
      }

      // Limpar o timer de desconexão forçada
      if (this.forceDisconnectTimer) {
        clearTimeout(this.forceDisconnectTimer);
        this.forceDisconnectTimer = undefined;
      }

      Logger.log('Desconectando sessão SSH');

      // Tratamento especial para dispositivos Nokia antes de desconectar
      if (this.deviceType === Device.NOKIA && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Dispositivo Nokia: desconectando sem enviar comando de logout');
          // Para Nokia, não enviar comando de logout para evitar o erro "Unable to exec"
          // Apenas registrar a intenção de desconectar

          // Pequena pausa antes de desconectar para permitir que qualquer operação pendente seja concluída
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (e) {
          Logger.error('Erro ao preparar desconexão do Nokia:', e);
          // Não propagar o erro, apenas registrar
        }
      }

      // Para dispositivos Mikrotik, enviar comando de quit antes de desconectar
      if (this.deviceType === Device.MIKROTIK && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Enviando comando de quit para dispositivo Mikrotik');
          // Executar comando de quit e aguardar um curto período
          await this.ssh.execCommand('quit', { cwd: '/' });

          // Pequena pausa para permitir que o comando seja processado
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (e) {
          Logger.error('Erro ao enviar quit para Mikrotik:', e);
        }
      }

      // Desconectar e limpar recursos
      this.ssh.dispose();
      this.isConnected = false;
      this.connectionAttempts = 0;
      this.ssh = new NodeSSH();
      Logger.log('Sessão SSH encerrada com sucesso');
    } catch (error) {
      Logger.error('Erro ao desconectar sessão SSH:', error);
      // Garantir que os recursos sejam liberados mesmo em caso de erro
      this.isConnected = false;
      this.connectionAttempts = 0;
      this.ssh = new NodeSSH();
    }
  }

  /**
   * Verifica se está conectado e pronto para executar comandos
   */
  isConnectedAndReady(): boolean {
    return this.isConnected && !!this.ssh.connection && !this.ssh.connection.closing;
  }

  /**
   * Obtém a instância SSH
   */
  getSSH(): NodeSSH {
    return this.ssh;
  }

  /**
   * Obtém o tipo de dispositivo
   */
  getDeviceType(): Device {
    return this.deviceType;
  }

  /**
   * Obtém o último servidor conectado
   */
  getLastServer(): Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'> | undefined {
    return this.lastServer;
  }
}
