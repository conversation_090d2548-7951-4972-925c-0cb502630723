import React, { useState, Fragment } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { User } from '../../types/user'
import { userService } from '../../services/userService'
import { UserForm } from '../../components/UserForm'
import { toast } from 'react-hot-toast'
import { Dialog, Transition } from '@headlessui/react'
import { Users, UserPlus, X, UserCheck, UserX, Filter, Edit, RefreshCw } from 'lucide-react'
import ConfirmModal from '../../components/ConfirmModal'

export function UsersPage() {
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [showInactive, setShowInactive] = useState(false)
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [isConfirmDeactivateOpen, setIsConfirmDeactivateOpen] = useState(false)
  const [isConfirmReactivateOpen, setIsConfirmReactivateOpen] = useState(false)
  const [userIdToAction, setUserIdToAction] = useState<string | null>(null)
  const queryClient = useQueryClient()

  const { data: users, isLoading } = useQuery<User[]>({
    queryKey: ['users', showInactive],
    queryFn: () => userService.getAll(showInactive)
  })

  const createMutation = useMutation({
    mutationFn: userService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Usuário criado com sucesso!')
      closeModal()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao criar usuário')
    }
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => userService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Usuário atualizado com sucesso!')
      closeModal()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao atualizar usuário')
    }
  })

  const deleteMutation = useMutation({
    mutationFn: userService.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Usuário excluído com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao excluir usuário')
    }
  })

  const deactivateMutation = useMutation({
    mutationFn: userService.deactivate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Usuário desativado com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao desativar usuário')
    }
  })

  const reactivateMutation = useMutation({
    mutationFn: userService.reactivate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Usuário reativado com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao reativar usuário')
    }
  })

  const handleCreateUser = (data: any) => {
    createMutation.mutate(data)
  }

  const handleUpdateUser = (data: any) => {
    if (selectedUser) {
      const updatedData = {
        ...data,
        active: selectedUser.active
      }
      updateMutation.mutate({ id: selectedUser.id, data: updatedData })
    }
  }

  const handleDeleteUser = (id: string) => {
    setUserIdToAction(id)
    setIsConfirmDeleteOpen(true)
  }

  const confirmDeleteUser = () => {
    if (userIdToAction) {
      deleteMutation.mutate(userIdToAction)
    }
  }

  const handleDeactivateUser = (id: string) => {
    // Verificar se é o último administrador ativo
    const isLastActiveAdmin = () => {
      const activeAdmins = users?.filter(u => u.active && u.role === 'ADMIN') || []
      const userToDeactivate = users?.find(u => u.id === id)
      return activeAdmins.length === 1 && userToDeactivate?.role === 'ADMIN' && userToDeactivate?.active
    }

    if (isLastActiveAdmin()) {
      toast.error('Não é possível desativar o último administrador ativo do sistema')
      return
    }

    setUserIdToAction(id)
    setIsConfirmDeactivateOpen(true)
  }

  const confirmDeactivateUser = () => {
    if (userIdToAction) {
      deactivateMutation.mutate(userIdToAction)
    }
  }

  const handleReactivateUser = (id: string) => {
    setUserIdToAction(id)
    setIsConfirmReactivateOpen(true)
  }

  const confirmReactivateUser = () => {
    if (userIdToAction) {
      reactivateMutation.mutate(userIdToAction)
    }
  }

  const openModal = (user: User | null = null) => {
    setSelectedUser(user)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedUser(null)
  }

  const toggleShowInactive = () => {
    setShowInactive(!showInactive)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6">
      <div className="responsive-flex justify-between mb-4 sm:mb-6">
        <div className="flex items-center gap-2 mb-4 sm:mb-0">
          <Users className="h-6 w-6 text-blue-600" />
          <h1 className="responsive-heading text-gray-900">Gerenciamento de Usuários</h1>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 sm:space-x-2 w-full sm:w-auto">
          <button
            onClick={toggleShowInactive}
            className={`responsive-button ${
              showInactive ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
            }`}
            title={showInactive ? 'Ocultar usuários inativos' : 'Mostrar usuários inativos'}
          >
            <Filter className="h-5 w-5" />
            <span className="ml-1">{showInactive ? 'Todos os usuários' : 'Apenas ativos'}</span>
          </button>
          <button
            onClick={() => openModal()}
            className="responsive-button bg-blue-600 text-white hover:bg-blue-700"
          >
            <UserPlus className="h-5 w-5" />
            <span className="ml-1">Novo Usuário</span>
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="responsive-table-container">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nome
                  </th>
                  <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                    Email
                  </th>
                  <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Função
                  </th>
                  <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-3 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users && users.length > 0 ? (
                  users.map((user) => (
                    <tr key={user.id} className={!user.active ? 'bg-gray-100' : 'hover:bg-gray-50'}>
                      <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                        <div className="text-xs sm:text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-xs text-gray-500 sm:hidden truncate">{user.email}</div>
                      </td>
                      <td className="px-3 sm:px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                        <div className="text-xs sm:text-sm text-gray-500">{user.email}</div>
                      </td>
                      <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'}`}>
                          {user.role === 'ADMIN' ? 'Admin' : 'Usuário'}
                        </span>
                      </td>
                      <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {user.active ? 'Ativo' : 'Inativo'}
                        </span>
                      </td>
                      <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => openModal(user)}
                            className="text-blue-600 hover:text-blue-900 flex items-center"
                            title="Editar"
                          >
                            <Edit className="h-4 w-4 sm:hidden" />
                            <span className="hidden sm:inline">Editar</span>
                          </button>
                          {user.active ? (
                            <button
                              onClick={() => handleDeactivateUser(user.id)}
                              className="text-orange-600 hover:text-orange-900 flex items-center"
                              title="Desativar"
                            >
                              <UserX className="h-4 w-4 sm:hidden" />
                              <span className="hidden sm:inline">Desativar</span>
                            </button>
                          ) : (
                            <button
                              onClick={() => handleReactivateUser(user.id)}
                              className="text-green-600 hover:text-green-900 flex items-center"
                              title="Reativar"
                            >
                              <RefreshCw className="h-4 w-4 sm:hidden" />
                              <span className="hidden sm:inline">Reativar</span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-3 sm:px-6 py-4 text-center text-xs sm:text-sm text-gray-500">
                      <div className="flex flex-col items-center justify-center py-6">
                        <Users className="h-12 w-12 text-gray-400 mb-2" />
                        <p>Nenhum usuário encontrado</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Modal de formulário de usuário */}
      <Transition appear show={isModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex justify-between items-center mb-4">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900 flex items-center"
                    >
                      {selectedUser ? (
                        <>
                          <Users className="h-5 w-5 mr-2" />
                          Editar Usuário
                        </>
                      ) : (
                        <>
                          <UserPlus className="h-5 w-5 mr-2" />
                          Novo Usuário
                        </>
                      )}
                    </Dialog.Title>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500"
                      onClick={closeModal}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                  <UserForm
                    initialData={selectedUser}
                    onSubmit={selectedUser ? handleUpdateUser : handleCreateUser}
                    onCancel={closeModal}
                    isEditing={!!selectedUser}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Modal de confirmação de exclusão */}
      <ConfirmModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={confirmDeleteUser}
        title="Excluir Usuário"
        message="Tem certeza que deseja excluir este usuário? Esta ação não pode ser desfeita."
        confirmText="Excluir"
        confirmStyle="danger"
      />

      {/* Modal de confirmação de desativação */}
      <ConfirmModal
        isOpen={isConfirmDeactivateOpen}
        onClose={() => setIsConfirmDeactivateOpen(false)}
        onConfirm={confirmDeactivateUser}
        title="Desativar Usuário"
        message="Tem certeza que deseja desativar este usuário? Ele não poderá mais acessar o sistema."
        confirmText="Desativar"
        confirmStyle="warning"
      />

      {/* Modal de confirmação de reativação */}
      <ConfirmModal
        isOpen={isConfirmReactivateOpen}
        onClose={() => setIsConfirmReactivateOpen(false)}
        onConfirm={confirmReactivateUser}
        title="Reativar Usuário"
        message="Tem certeza que deseja reativar este usuário? Ele poderá acessar o sistema novamente."
        confirmText="Reativar"
        confirmStyle="primary"
      />
    </div>
  )
} 