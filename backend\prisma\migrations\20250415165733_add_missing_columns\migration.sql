/*
  Warnings:

  - Added the required column `commandVersion` to the `CommandHistory` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Command" ADD COLUMN     "isLatest" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "previousVersionId" TEXT,
ADD COLUMN     "version" INTEGER NOT NULL DEFAULT 1;

-- AlterTable
ALTER TABLE "CommandHistory" ADD COLUMN     "commandVersion" INTEGER NOT NULL,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "Server" ADD COLUMN     "active" BOOLEAN NOT NULL DEFAULT true;

-- CreateIndex
CREATE INDEX "Command_serverId_isLatest_idx" ON "Command"("serverId", "isLatest");

-- CreateIndex
CREATE INDEX "CommandHistory_createdAt_idx" ON "CommandHistory"("createdAt");

-- CreateIndex
CREATE INDEX "CommandHistory_serverId_createdAt_idx" ON "CommandHistory"("serverId", "createdAt");

-- CreateIndex
CREATE INDEX "Server_active_idx" ON "Server"("active");

-- AddForeignKey
ALTER TABLE "Command" ADD CONSTRAINT "Command_previousVersionId_fkey" FOREIGN KEY ("previousVersionId") REFERENCES "Command"("id") ON DELETE SET NULL ON UPDATE CASCADE;
