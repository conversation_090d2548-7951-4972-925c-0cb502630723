import { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { 
  listCommandTemplates, 
  getCommandTemplate, 
  createCommandTemplate, 
  updateCommandTemplate, 
  deleteCommandTemplate,
  applyTemplateToServer
} from '../controllers/commandTemplates'
import { verifyJWT } from '../middlewares/auth'

export async function commandTemplateRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Listar templates de comandos
  app.get('/', listCommandTemplates)

  // Obter template por ID
  app.get('/:id', getCommandTemplate)

  // Criar template de comandos
  app.post('/', createCommandTemplate)

  // Atualizar template de comandos
  app.put('/:id', updateCommandTemplate)

  // Excluir template de comandos
  app.delete('/:id', deleteCommandTemplate)

  // Aplicar template a um servidor
  app.post('/:id/apply/:serverId', applyTemplateToServer)
} 