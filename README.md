# Sem Fronteiras - Gerenciador SSH/RDP

Sistema web para gerenciamento e execução de comandos SSH/RDP em servidores remotos, permitindo administração centralizada de múltiplos servidores através de uma interface web intuitiva e segura.

## 📚 Documentação

- [Especificações do Projeto](./project_specs.md) - Detalhes completos sobre funcionalidades, roadmap e tecnologias
- [Estrutura do Banco de Dados](./db_structure.md) - Documentação detalhada do schema e relacionamentos

## 🚀 Início Rápido

### Pré-requisitos
- Docker e Docker Compose
- Node.js 18+ (para desenvolvimento local)
- PostgreSQL (opcional, se não usar Docker)

### Instalação com Docker

1. Clone o repositório
```bash
git clone [url-do-repositorio]
cd sem-fronteiras
```

2. Configure as variáveis de ambiente
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

3. Inicie os containers
```bash
docker-compose up -d
```

4. Acesse a aplicação
- Frontend: http://localhost:5173
- Backend: http://localhost:3000

### Desenvolvimento Local

1. Backend
```bash
cd backend
npm install
npm run dev
```

2. Frontend
```bash
cd frontend
npm install
npm run dev
```

### Configuração do Banco de Dados

1. Crie o banco de dados
```bash
docker-compose exec postgres psql -U postgres -c "CREATE DATABASE sem_fronteiras;"
```

2. Execute as migrações
```bash
docker-compose exec backend npx prisma migrate deploy
docker-compose exec backend npx prisma db seed