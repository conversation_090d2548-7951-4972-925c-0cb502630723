/**
 * Wrapper simples para console.* para permitir futura troca por biblioteca de logging
 */
export class Logger {
  static log(message: string, ...args: any[]): void {
    console.log(message, ...args);
  }

  static error(message: string, ...args: any[]): void {
    console.error(message, ...args);
  }

  static warn(message: string, ...args: any[]): void {
    console.warn(message, ...args);
  }

  static info(message: string, ...args: any[]): void {
    console.info(message, ...args);
  }

  static debug(message: string, ...args: any[]): void {
    console.debug(message, ...args);
  }
}
