# Plano para Resolução da Paginação em Comandos SSH

Este documento detalha o plano para resolver o problema da paginação incompleta na saída de comandos SSH, conforme especificado na tarefa.

## Contexto

A ferramenta atual de execução de comandos SSH não captura a saída completa de comandos em alguns servidores, devido à necessidade de interação manual (pressionar Enter/Espaço) para continuar a exibição.

## Objetivo

Implementar uma solução que detecte automaticamente prompts de paginação e simule o pressionamento das teclas Enter e/ou Espaço para garantir a captura completa da saída.

## Plano Detalhado

1.  **Modificar a função `executeCommand` em `backend/src/services/ssh.ts`:**

    *   Dentro do bloco `if (needsInteractive)`, modificar o manipulador `shell.on('data', ...)` para detectar prompts de paginação.
    *   Criar uma expressão regular para identificar os prompts: `/(--\s*More\s*--)|(press\s+Enter)|(load\s+more)/i`. O modificador `i` garante a detecção independente de capitalização.
    *   Se um prompt for detectado:
        *   Enviar `shell.write('\n')` (Enter) ou `shell.write(' ')` (Espaço). A escolha pode depender do tipo de servidor (informação disponível no objeto `server` da função `connect`).
        *   Aumentar o timeout dinamicamente. Em vez de um timeout fixo, usar um timeout inicial e incrementá-lo a cada detecção de prompt.
        *   Aguardar 500ms.
        *   Verificar novamente por prompts.
    *   Manter um buffer da saída e continuar anexando a ele.

2.  **Testes:**

    *   Criar testes unitários para a função `executeCommand` modificada, usando mocks para simular diferentes cenários de paginação e timeouts.
    *   Testar com diferentes tipos de servidores (especialmente Nokia, mencionado no problema) para garantir que a lógica de detecção de prompt e envio de continuação funcione corretamente.

## Diagrama de Fluxo

```mermaid
graph TD
    A[Início] --> B{Comando precisa de shell interativo?};
    B -- Sim --> C[Obter shell interativo];
    C --> D[Configurar terminal VT100];
    D --> E[Registrar listener 'data'];
    E --> F{Detectar prompt de paginação};
    F -- Sim --> G[Enviar Enter/Espaço];
    G --> H[Aumentar timeout];
    H --> I[Aguardar 500ms];
    I --> F;
    F -- Não --> J{Timeout?};
    J -- Sim --> K[Fechar shell];
    K --> L[Retornar saída];
    J -- Não --> E;
    B -- Não --> M[Executar comando simples];
    M --> N[Retornar saída];