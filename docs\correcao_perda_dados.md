# Documentação: Correção do Problema de Perda de Dados

## Problema Identificado

Foi identificado um problema onde os dados do banco de dados estavam sendo apagados sem explicação aparente. Após investigação detalhada, foram encontradas duas causas principais:

1. **Script `update-commands.ts`**: Este script estava apagando todos os comandos existentes para cada servidor e recriando apenas os comandos padrão.

2. **Método `updateServer` no arquivo `controllers/servers.ts`**: Durante a atualização de um servidor, todos os comandos associados eram excluídos e apenas os comandos incluídos na requisição eram recriados.

## Alterações Realizadas

### 1. Modificação do script `update-commands.ts`

O script foi modificado para preservar os comandos personalizados. Agora, em vez de excluir todos os comandos existentes, ele:

- Verifica quais comandos padrão estão faltando para cada servidor
- Adiciona apenas os comandos padrão que não existem
- Mantém todos os comandos personalizados intactos

**Antes:**
```typescript
// Deletar comandos existentes
await prisma.command.deleteMany({
  where: { serverId: server.id }
})

// Criar novos comandos
await prisma.command.createMany({
  data: updatedCommands.map(cmd => ({
    ...cmd,
    serverId: server.id
  }))
})
```

**Depois:**
```typescript
// Buscar comandos existentes para o servidor
const existingCommands = await prisma.command.findMany({
  where: { serverId: server.id }
})

// Verificar quais comandos padrão estão faltando
const existingCommandNames = existingCommands.map(cmd => cmd.name)
const missingCommands = updatedCommands.filter(cmd => !existingCommandNames.includes(cmd.name))

// Adicionar apenas os comandos padrão que estão faltando
if (missingCommands.length > 0) {
  await prisma.command.createMany({
    data: missingCommands.map(cmd => ({
      ...cmd,
      serverId: server.id
    })),
    skipDuplicates: true
  })
}
```

### 2. Modificação do método `updateServer` no arquivo `controllers/servers.ts`

O método foi modificado para preservar os comandos existentes que não foram incluídos na atualização. Agora, em vez de excluir todos os comandos, ele:

- Identifica comandos a serem atualizados (já existentes)
- Identifica comandos a serem criados (novos)
- Mantém todos os comandos existentes que não foram mencionados na requisição

**Antes:**
```typescript
commands: {
  deleteMany: {},
  create: commands.map(cmd => ({
    name: cmd.name,
    command: cmd.command,
    description: cmd.description || null,
  })),
},
```

**Depois:**
```typescript
// Obter os comandos existentes
const existingCommands = await prisma.command.findMany({
  where: { serverId: id }
})

// Mapear os IDs dos comandos existentes por nome
const existingCommandMap = new Map()
existingCommands.forEach(cmd => {
  existingCommandMap.set(cmd.name, cmd.id)
})

// Identificar comandos a serem atualizados ou adicionados
const commandsToUpdate = []
const commandsToCreate = []

// Processar os comandos recebidos na requisição
for (const cmd of commands) {
  if (existingCommandMap.has(cmd.name)) {
    // Comando existente - atualizar
    commandsToUpdate.push({
      id: existingCommandMap.get(cmd.name),
      name: cmd.name,
      command: cmd.command,
      description: cmd.description || null,
    })
  } else {
    // Novo comando - criar
    commandsToCreate.push({
      name: cmd.name,
      command: cmd.command,
      description: cmd.description || null,
      serverId: id
    })
  }
}

// Atualizar comandos existentes
for (const cmd of commandsToUpdate) {
  await prisma.command.update({
    where: { id: cmd.id },
    data: {
      name: cmd.name,
      command: cmd.command,
      description: cmd.description,
    }
  })
}

// Criar novos comandos
if (commandsToCreate.length > 0) {
  await prisma.command.createMany({
    data: commandsToCreate
  })
}
```

### 3. Implementação de verificação de integridade do banco de dados

Foi adicionada uma função de verificação de integridade do banco de dados que é executada na inicialização do servidor. Esta função:

- Verifica se existem usuários no banco de dados
- Verifica se existem servidores no banco de dados
- Verifica se existem comandos no banco de dados
- Identifica servidores sem comandos
- Gera alertas quando problemas são encontrados

```typescript
// Função para verificar a integridade do banco de dados
async function checkDatabaseIntegrity() {
  try {
    console.log('Verificando integridade do banco de dados...')
    
    // Verificar se existem usuários
    const userCount = await prisma.user.count()
    console.log(`Usuários encontrados: ${userCount}`)
    
    if (userCount === 0) {
      console.warn('ALERTA: Nenhum usuário encontrado no banco de dados!')
    }
    
    // Verificar se existem servidores
    const serverCount = await prisma.server.count()
    console.log(`Servidores encontrados: ${serverCount}`)
    
    // Verificar se existem comandos
    const commandCount = await prisma.command.count()
    console.log(`Comandos encontrados: ${commandCount}`)
    
    // Verificar se existem servidores sem comandos
    const serversWithoutCommands = await prisma.server.findMany({
      where: {
        commands: {
          none: {}
        }
      },
      select: {
        id: true,
        name: true
      }
    })
    
    if (serversWithoutCommands.length > 0) {
      console.warn('ALERTA: Encontrados servidores sem comandos:')
      serversWithoutCommands.forEach(server => {
        console.warn(`- ${server.name} (${server.id})`)
      })
    }
    
    console.log('Verificação de integridade concluída!')
  } catch (error) {
    console.error('Erro ao verificar banco de dados:', error)
  }
}
```

### 4. Criação de scripts de manutenção

Foram criados dois scripts para auxiliar na manutenção do banco de dados:

1. **Script de restauração de backup**:
   - Encontra o backup mais recente
   - Restaura o backup para o banco de dados
   - Fornece feedback sobre o processo

2. **Script de verificação do banco de dados**:
   - Verifica a integridade do banco de dados
   - Identifica problemas potenciais
   - Fornece recomendações para correção

## Próximos Passos

1. **Implementar sistema de migração segura**:
   - Desenvolver um sistema que preserve dados existentes durante migrações
   - Adicionar validação de esquema antes de aplicar migrações
   - Implementar rollback automático em caso de falha

2. **Adicionar validação adicional antes de operações de exclusão**:
   - Implementar confirmações adicionais para operações destrutivas
   - Adicionar logs detalhados para todas as operações de exclusão
   - Criar sistema de "lixeira" para recuperação de dados excluídos

3. **Criar sistema de auditoria para rastrear alterações no banco de dados**:
   - Registrar todas as operações de criação, atualização e exclusão
   - Armazenar informações sobre o usuário que realizou a operação
   - Implementar interface para visualização do histórico de alterações

4. **Implementar backups incrementais**:
   - Reduzir o espaço de armazenamento necessário para backups
   - Diminuir o tempo necessário para criar backups
   - Manter histórico mais detalhado de alterações

5. **Adicionar interface administrativa para gerenciamento de backups**:
   - Permitir visualização dos backups disponíveis
   - Facilitar a restauração de backups específicos
   - Fornecer métricas sobre o tamanho e frequência dos backups

## Conclusão

As alterações realizadas resolvem o problema de perda de dados identificado, implementando uma abordagem mais segura para manipulação dos dados do banco. A verificação de integridade adicionada na inicialização do servidor ajudará a identificar problemas rapidamente, permitindo uma resposta mais rápida a problemas futuros.

Os próximos passos sugeridos visam melhorar ainda mais a robustez do sistema, implementando práticas recomendadas para gerenciamento de dados e garantindo a integridade e disponibilidade das informações armazenadas.
