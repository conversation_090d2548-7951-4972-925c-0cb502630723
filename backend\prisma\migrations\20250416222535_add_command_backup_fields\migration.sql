-- DropForeignKey
ALTER TABLE "CommandHistory" DROP CONSTRAINT "CommandHistory_commandId_fkey";

-- AlterTable
ALTER TABLE "CommandHistory" ADD COLUMN     "commandName" TEXT,
ADD COLUMN     "commandText" TEXT,
ALTER COLUMN "commandId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "CommandHistory" ADD CONSTRAINT "CommandHistory_commandId_fkey" FOREIGN KEY ("commandId") REFERENCES "Command"("id") ON DELETE SET NULL ON UPDATE CASCADE;
