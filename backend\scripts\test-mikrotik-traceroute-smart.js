const { NodeSSH } = require('node-ssh');
const { MikrotikExecutor } = require('../dist/services/ssh/executors/mikrotikExecutor');
const { Logger } = require('../dist/utils/logger');

async function testTracerouteSmartMonitoring() {
  const ssh = new NodeSSH();
  
  try {
    // Conectar ao Mikrotik
    Logger.log('🔌 Conectando ao Mikrotik...');
    await ssh.connect({
      host: '***********',
      username: 'admin',
      password: '88701181Sem*',
      port: 22,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1'],
        compress: ['none']
      }
    });
    
    Logger.log('✅ Conectado com sucesso!');
    
    // Criar executor
    const executor = new MikrotikExecutor(ssh);
    
    // Testes específicos para traceroute com monitoramento inteligente
    const tests = [
      {
        name: 'Traceroute Google DNS - Monitoramento Inteligente',
        command: '/tool traceroute address=*******',
        description: 'Teste com monitoramento de progresso em tempo real',
        expectedFeatures: [
          'Detecção de progresso por hop',
          'Finalização por falta de atividade',
          'Limite de 30 hops automático',
          'Timeout de 3s por hop'
        ]
      },
      {
        name: 'Traceroute Cloudflare - Teste Limites Automáticos',
        command: 'traceroute *******',
        description: 'Comando simples que deve ser convertido com limites automáticos',
        expectedFeatures: [
          'Conversão para /tool traceroute address=******* max-hops=30 timeout=3s',
          'Monitoramento inteligente de progresso',
          'Detecção de conclusão baseada em análise'
        ]
      },
      {
        name: 'Traceroute Local Gateway - Teste Resposta Rápida',
        command: '/tool traceroute address=***********',
        description: 'Teste com destino local que deve responder rapidamente',
        expectedFeatures: [
          'Detecção rápida de conclusão',
          'Resposta do destino detectada',
          'Finalização antes do timeout'
        ]
      }
    ];
    
    for (const test of tests) {
      Logger.log(`\n🎯 === ${test.name} ===`);
      Logger.log(`📝 Comando: ${test.command}`);
      Logger.log(`📋 Descrição: ${test.description}`);
      Logger.log(`🔍 Recursos esperados:`);
      test.expectedFeatures.forEach(feature => {
        Logger.log(`  • ${feature}`);
      });
      
      try {
        const startTime = Date.now();
        
        Logger.log('\n🚀 Executando comando com monitoramento inteligente...');
        const result = await executor.executeCommand(test.command);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        Logger.log(`\n⏱️ === RESULTADOS ===`);
        Logger.log(`⏱️ Duração: ${duration}ms`);
        Logger.log(`📊 Código de saída: ${result.code}`);
        
        if (result.stdout) {
          Logger.log('\n📤 === SAÍDA COMPLETA ===');
          console.log(result.stdout);
          Logger.log('=== FIM DA SAÍDA ===\n');
          
          // Análise específica do traceroute
          const lines = result.stdout.split('\n').filter(line => line.trim().length > 0);
          const hopLines = lines.filter(line => /^\s*\d+\s+/.test(line));
          const hasTableHeader = result.stdout.includes('ADDRESS') && result.stdout.includes('LOSS');
          const hasHopData = hopLines.length > 0;
          const hasTimeouts = result.stdout.includes('100%') || result.stdout.includes('timeout');
          const hasResponses = result.stdout.includes('ms') && !result.stdout.includes('100%');
          
          Logger.log('🔍 === ANÁLISE DO TRACEROUTE ===');
          Logger.log(`  ${hasTableHeader ? '✅' : '❌'} Cabeçalho da tabela: ${hasTableHeader ? 'ENCONTRADO' : 'NÃO ENCONTRADO'}`);
          Logger.log(`  ${hasHopData ? '✅' : '❌'} Dados de hops: ${hasHopData ? `${hopLines.length} hops encontrados` : 'NÃO ENCONTRADOS'}`);
          Logger.log(`  ${hasTimeouts ? '⚠️' : '✅'} Timeouts: ${hasTimeouts ? 'DETECTADOS' : 'NENHUM'}`);
          Logger.log(`  ${hasResponses ? '✅' : '❌'} Respostas válidas: ${hasResponses ? 'ENCONTRADAS' : 'NÃO ENCONTRADAS'}`);
          
          // Análise de conclusão
          const isComplete = result.code === 0 && hasHopData;
          const wasTimeout = result.code === 124 || result.stdout.includes('[TIMEOUT');
          const wasStagnation = result.stdout.includes('[SEM PROGRESSO]');
          
          Logger.log('\n🏁 === ANÁLISE DE CONCLUSÃO ===');
          if (isComplete) {
            Logger.log('🎉 TRACEROUTE COMPLETADO COM SUCESSO');
            Logger.log(`  • Duração: ${duration}ms`);
            Logger.log(`  • Hops capturados: ${hopLines.length}`);
            Logger.log(`  • Método de conclusão: ${hasResponses ? 'Resposta do destino' : 'Análise de padrões'}`);
          } else if (wasTimeout) {
            Logger.log('⏰ TRACEROUTE FINALIZADO POR TIMEOUT');
            Logger.log(`  • Dados parciais capturados: ${hopLines.length} hops`);
            Logger.log(`  • Duração até timeout: ${duration}ms`);
          } else if (wasStagnation) {
            Logger.log('🔄 TRACEROUTE FINALIZADO POR FALTA DE PROGRESSO');
            Logger.log(`  • Dados capturados: ${hopLines.length} hops`);
            Logger.log(`  • Duração até estagnação: ${duration}ms`);
          } else {
            Logger.log('❓ TRACEROUTE FINALIZADO POR MOTIVO DESCONHECIDO');
          }
          
          // Verificar eficiência do tempo
          if (duration < 15000) {
            Logger.log('⚡ EXCELENTE: Finalização rápida (< 15s)');
          } else if (duration < 25000) {
            Logger.log('✅ BOM: Finalização em tempo razoável (< 25s)');
          } else {
            Logger.log('⚠️ LENTO: Finalização demorada (> 25s)');
          }
          
        } else {
          Logger.log('❌ Nenhuma saída retornada');
        }
        
        if (result.stderr) {
          Logger.log('\n⚠️ === STDERR ===');
          console.log(result.stderr);
          Logger.log('=== FIM STDERR ===\n');
        }
        
      } catch (error) {
        Logger.error(`❌ Erro no teste: ${error.message}`);
        
        if (error.message.includes('timeout') || error.message.includes('Timeout')) {
          Logger.error('🕐 TIMEOUT DETECTADO - O monitoramento inteligente pode precisar de ajustes');
        }
      }
      
      // Pausa entre testes
      Logger.log('⏸️ Aguardando 5 segundos antes do próximo teste...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    Logger.log('\n🏁 === RESUMO DOS TESTES ===');
    Logger.log('✅ Testes de monitoramento inteligente do traceroute concluídos');
    Logger.log('📊 Recursos testados:');
    Logger.log('  • Monitoramento de progresso em tempo real');
    Logger.log('  • Detecção de estagnação (15s sem progresso)');
    Logger.log('  • Timeout absoluto (30s máximo)');
    Logger.log('  • Análise inteligente de conclusão');
    Logger.log('  • Limites automáticos (max-hops=30, timeout=3s)');
    
  } catch (error) {
    Logger.error('❌ Erro durante os testes:', error);
  } finally {
    try {
      await ssh.dispose();
      Logger.log('🔌 Conexão SSH encerrada');
    } catch (error) {
      Logger.error('Erro ao encerrar SSH:', error);
    }
  }
}

// Executar os testes
testTracerouteSmartMonitoring().catch(console.error);
