import axios from 'axios'

export const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Adiciona o token em todas as requisições se ele existir
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('sem-fronteiras:token')
  
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  
  return config
})

// Intercepta erros de autenticação
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('sem-fronteiras:token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
) 