FROM node:18-alpine

WORKDIR /app

# Instalar dependências necessárias
RUN apk add --no-cache openssl

# Copiar arquivos de configuração
COPY package*.json ./
COPY prisma ./prisma/

# Instalar dependências e gerar cliente Prisma
RUN npm install
RUN npx prisma generate

# Copiar código fonte
COPY . .

# Criar diretório de logs
RUN mkdir -p /app/logs && chmod 755 /app/logs

# Tornar os scripts executáveis
RUN chmod +x /app/scripts/*.sh
RUN chmod +x /app/scripts/*.js

# Expor porta da aplicação
EXPOSE 3000

# Configurar variáveis de ambiente para Node.js
ENV NODE_OPTIONS="--max-old-space-size=2048"

# Usar script de inicialização
ENTRYPOINT ["/app/scripts/startup-checks.sh"]

# Comando para iniciar a aplicação em modo de desenvolvimento
CMD ["npm", "run", "dev"]