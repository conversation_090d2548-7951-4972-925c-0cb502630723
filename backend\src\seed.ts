import { SSHServer } from './types/server'
import { randomUUID } from 'crypto'

const basicCommands = [
  {
    id: randomUUID(),
    name: 'Listar arquivos',
    command: 'ls -la',
    description: 'Lista todos os arquivos e diretórios com detalhes'
  },
  {
    id: randomUUID(),
    name: 'Mostrar diretório atual',
    command: 'pwd',
    description: 'Exibe o caminho completo do diretório atual'
  },
  {
    id: randomUUID(),
    name: 'Informações do sistema',
    command: 'uname -a',
    description: 'Mostra informações detalhadas do sistema operacional'
  },
  {
    id: randomUUID(),
    name: 'Uso do disco',
    command: 'df -h',
    description: 'Mostra o uso do disco em formato legível'
  },
  {
    id: randomUUID(),
    name: 'Processos em execução',
    command: 'ps -eo pid,ppid,user,%cpu,%mem,stat,start,time,command --sort=-%cpu',
    description: 'Lista os processos em execução, ordenados por uso de CPU'
  },
  {
    id: randomUUID(),
    name: 'Us<PERSON> de memória',
    command: 'free -h',
    description: 'Mostra informações detalhadas sobre o uso de memória'
  },
  {
    id: randomUUID(),
    name: 'Carga do sistema',
    command: 'uptime',
    description: 'Mostra o tempo de atividade e carga média do sistema'
  }
]

export const initialServers: SSHServer[] = [
  {
    id: randomUUID(),
    name: 'Servidor Linux',
    host: '***********',
    port: 43999,
    username: 'semfronteiras',
    password: '88701181Sem*',
    os: 'LINUX',
    commands: basicCommands
  }
] 