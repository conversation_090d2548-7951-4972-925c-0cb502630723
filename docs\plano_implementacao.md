# Plano de Implementação do Microserviço Python SSH

## Resumo da Solução

Implementaremos um microserviço Python dedicado para lidar com os equipamentos de rede problemáticos (HarmonyOS), mantendo o backend Node.js existente para todas as outras funcionalidades. Esta abordagem híbrida permitirá resolver o problema crítico de travamentos sem a necessidade de uma reescrita completa do sistema.

## Diagrama da Arquitetura

```mermaid
graph TD
    A[Frontend React] --> B[API Node.js Existente]
    B --> C[Banco de Dados PostgreSQL]
    B --> D[Microserviço Python]
    D --> E[Equipamentos HarmonyOS/Problemáticos]
    B --> F[Servidores Sem Problemas]
    
    subgraph "Comunicação"
        B <--> |REST API| D
    end
```

## Estrutura do Projeto Python

```
python-ssh-service/
├── Dockerfile
├── requirements.txt
├── main.py
├── app/
│   ├── __init__.py
│   ├── config.py
│   ├── models.py
│   ├── routers/
│   │   ├── __init__.py
│   │   └── ssh.py
│   └── services/
│       ├── __init__.py
│       └── ssh_service.py
├── tests/
│   ├── __init__.py
│   └── test_ssh.py
└── logs/
```

## Tarefas de Implementação

### Fase 1: Configuração do Projeto Python

1. **Criar estrutura de diretórios**
2. **Configurar ambiente virtual Python**
3. **Instalar dependências básicas:**
   - FastAPI
   - Uvicorn
   - Netmiko
   - Pydantic
   - Python-dotenv
4. **Configurar Dockerfile e .dockerignore**
5. **Configurar .env com variáveis necessárias**

### Fase 2: Implementação Core do Serviço Python

1. **Implementar models.py com esquemas de solicitação/resposta**
2. **Implementar config.py para gerenciar configurações**
3. **Implementar ssh_service.py com a lógica de conexão SSH**
4. **Implementar rotas API em ssh.py**
5. **Configurar app principal em main.py**
6. **Adicionar logging estruturado**

### Fase 3: Testes e Validação

1. **Implementar testes unitários para o serviço SSH**
2. **Configurar testes de integração com mock de dispositivos**
3. **Validar funcionalidade com equipamentos reais em ambiente de teste**
4. **Documentar resultados e comparar com solução Node.js existente**

### Fase 4: Integração com o Backend Node.js

1. **Implementar cliente Node.js para o serviço Python**
2. **Modificar o serviço SSH existente para rotear requisições**
3. **Implementar lógica de fallback em caso de falha**
4. **Adicionar monitoring e métricas**

### Fase 5: Deployment e Configuração

1. **Atualizar docker-compose.yml para incluir o serviço Python**
2. **Configurar rede para comunicação entre containers**
3. **Implementar health checks**
4. **Configurar logs centralizados**

## Detalhes de Implementação Chave

### Código Node.js para Integração (a ser implementado)

```typescript
// backend/src/services/ssh-python.ts
import axios from 'axios';
import { SSHServer, CommandResult } from '../types/server';
import { SSHService } from './ssh';

export class SSHServiceRouter {
  private nodeSSHService: SSHService;
  private pythonServiceURL: string;
  
  constructor() {
    this.nodeSSHService = new SSHService();
    this.pythonServiceURL = process.env.PYTHON_SSH_SERVICE_URL || 'http://python-ssh:8000';
  }
  
  async executeCommand(
    server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>,
    command: string
  ): Promise<CommandResult> {
    // Verificar se deve usar o serviço Python
    if (this.shouldUsePythonService(server)) {
      try {
        return await this.executePythonCommand(server, command);
      } catch (error) {
        console.error('Falha ao usar serviço Python, tentando Node.js:', error);
        // Fallback para implementação Node.js em caso de falha
        return await this.nodeSSHService.executeCommand(command);
      }
    }
    
    // Usar implementação Node.js existente
    return await this.nodeSSHService.executeCommand(command);
  }
  
  private shouldUsePythonService(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): boolean {
    // Lógica para determinar se deve usar o serviço Python
    // Exemplo: baseado no nome do servidor, sistema operacional ou configuração
    return server.name.toLowerCase().includes('harmony') ||
           server.host.startsWith('192.168.10.') || // Exemplo: sub-rede de equipamentos específicos
           process.env.FORCE_PYTHON_SSH === 'true';
  }
  
  private async executePythonCommand(
    server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>,
    command: string
  ): Promise<CommandResult> {
    try {
      // Determinar tipo de dispositivo
      let deviceType = 'linux';
      if (server.os === 'WINDOWS') {
        deviceType = 'windows';
      } else if (server.name.toLowerCase().includes('huawei') ||
                server.name.toLowerCase().includes('harmony')) {
        deviceType = 'huawei';
      }
      
      const response = await axios.post(`${this.pythonServiceURL}/ssh/execute`, {
        host: server.host,
        port: server.port,
        username: server.username,
        password: server.password,
        private_key: server.privateKey,
        command: command,
        device_type: deviceType,
        timeout: 60 // Timeout maior para comandos que podem demorar
      });
      
      return {
        stdout: response.data.stdout,
        stderr: response.data.stderr,
        code: response.data.code || 0
      };
    } catch (error) {
      console.error('Erro ao executar comando via serviço Python:', error);
      
      if (error.response) {
        return {
          stderr: `Erro do serviço Python: ${error.response.data.detail || 'Desconhecido'}`,
          code: error.response.status
        };
      }
      
      return {
        stderr: `Falha ao conectar ao serviço Python: ${error.message}`,
        code: 500
      };
    }
  }
}
```

### Dockerfile para o Serviço Python

```dockerfile
FROM python:3.10-slim

WORKDIR /app

# Instalar dependências do sistema para Netmiko
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Copiar requirements e instalar dependências
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código da aplicação
COPY . .

# Criar diretório de logs
RUN mkdir -p logs && chmod 777 logs

# Expor porta para API
EXPOSE 8000

# Comando para iniciar a aplicação
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Atualização do docker-compose.yml

```yaml
# Adicionar ao docker-compose.yml existente
services:
  # ... serviços existentes ...
  
  python-ssh:
    build:
      context: ./python-ssh-service
      dockerfile: Dockerfile
    container_name: python-ssh-service
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./python-ssh-service:/app
      - python_ssh_logs:/app/logs
    environment:
      - DEBUG=true
      - LOG_LEVEL=INFO
      - TOKEN_SECRET=${TOKEN_SECRET}
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

volumes:
  # ... volumes existentes ...
  python_ssh_logs:
```

## Próximos Passos para Modo Code

1. Iniciar criando a estrutura de diretórios para o projeto Python
2. Implementar o arquivo `requirements.txt` com as dependências necessárias
3. Desenvolver os principais componentes:
   - Configuração (config.py)
   - Modelos de dados (models.py)
   - Serviço SSH (ssh_service.py)
   - Rotas API (ssh.py)
   - Aplicação principal (main.py)
4. Criar o Dockerfile
5. Implementar a integração no lado Node.js

Após a implementação inicial, realizaremos testes com equipamentos HarmonyOS para validar que a solução resolve o problema de travamentos do Node.js.