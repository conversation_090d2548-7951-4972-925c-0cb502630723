import React, { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useQuery } from '@tanstack/react-query'
import { CommandTemplate, SSHServer } from '../types/server'
import { listServers, applyTemplateToServer } from '../services/api'
import { BookTemplate, Server, Check, X, AlertTriangle } from 'lucide-react'

interface ApplyTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  template: CommandTemplate
}

export default function ApplyTemplateModal({ isOpen, onClose, template }: ApplyTemplateModalProps) {
  const [selectedServerId, setSelectedServerId] = useState<string>('')
  const [isApplying, setIsApplying] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const { data: servers = [], isLoading } = useQuery({
    queryKey: ['servers'],
    queryFn: listServers,
  })

  async function handleApplyTemplate() {
    if (!selectedServerId) return

    try {
      setIsApplying(true)
      setResult(null)

      const response = await applyTemplateToServer(template.id, selectedServerId)
      
      setResult({
        success: true,
        message: `Template aplicado com sucesso! ${response.commands.length} comandos adicionados ao servidor.`,
      })
    } catch (error) {
      console.error('Erro ao aplicar template:', error)
      setResult({
        success: false,
        message: 'Erro ao aplicar template. Por favor, tente novamente.',
      })
    } finally {
      setIsApplying(false)
    }
  }

  function handleClose() {
    setSelectedServerId('')
    setResult(null)
    onClose()
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <BookTemplate className="h-6 w-6 text-primary-500" />
                    <h3 className="text-lg font-medium text-gray-900">
                      Aplicar Template a um Servidor
                    </h3>
                  </div>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <div className="mb-4">
                  <p className="text-sm text-gray-500">
                    Selecione um servidor para aplicar o template <strong>{template.name}</strong>.
                    Os comandos do template serão adicionados ao servidor selecionado.
                  </p>
                </div>

                {result ? (
                  <div className={`p-4 mb-4 rounded-md ${result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                    <div className="flex">
                      {result.success ? (
                        <Check className="h-5 w-5 text-green-400 mr-2" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                      )}
                      <p className="text-sm">{result.message}</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-4">
                      <label htmlFor="server" className="block text-sm font-medium text-gray-700 mb-1">
                        Servidor
                      </label>
                      {isLoading ? (
                        <div className="animate-pulse h-10 bg-gray-200 rounded"></div>
                      ) : servers.length === 0 ? (
                        <div className="text-sm text-gray-500">
                          Nenhum servidor disponível. Crie um servidor primeiro.
                        </div>
                      ) : (
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Server className="h-5 w-5 text-gray-400" />
                          </div>
                          <select
                            id="server"
                            value={selectedServerId}
                            onChange={(e) => setSelectedServerId(e.target.value)}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
                          >
                            <option value="">Selecione um servidor</option>
                            {servers.map((server) => (
                              <option key={server.id} value={server.id}>
                                {server.name} ({server.host})
                              </option>
                            ))}
                          </select>
                        </div>
                      )}
                    </div>

                    <div className="mb-4 border-t border-gray-200 pt-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                        <BookTemplate className="h-4 w-4 text-gray-500" />
                        Comandos no Template:
                      </h4>
                      <ul className="text-sm text-gray-600 space-y-1 ml-4 list-disc">
                        {template.commands.map((cmd) => (
                          <li key={cmd.id}>
                            <span className="font-medium">{cmd.name}</span>
                            {cmd.description && <span className="text-gray-500"> - {cmd.description}</span>}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </>
                )}

                <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={handleClose}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {result ? 'Fechar' : 'Cancelar'}
                  </button>
                  
                  {!result && (
                    <button
                      type="button"
                      onClick={handleApplyTemplate}
                      disabled={!selectedServerId || isApplying}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {isApplying ? 'Aplicando...' : 'Aplicar Template'}
                    </button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 