# Comandos Recomendados para Mikrotik RouterOS

Este documento contém uma lista de comandos que funcionam bem com dispositivos Mikrotik RouterOS através da interface SSH do sistema.

## Formato dos Comandos

Os comandos Mikrotik devem seguir o formato da CLI do RouterOS. A implementação atual adiciona automaticamente uma barra (/) no início do comando se ela não estiver presente.

## Comandos Básicos

### Informações do Sistema

```
/system resource print
/system identity print
/system routerboard print
/system license print
/system package print
/system history print
```

### Interfaces de Rede

```
/interface print
/interface ethernet print
/interface wireless print
/interface bridge print
/interface vlan print
```

### Endereços IP

```
/ip address print
/ip route print
/ip firewall filter print
/ip firewall nat print
/ip pool print
/ip dhcp-server print
/ip dns print
```

### Configuração Wireless

```
/interface wireless print
/interface wireless registration-table print
/interface wireless access-list print
/interface wireless connect-list print
```

### Usuários e Segurança

```
/user print
/user active print
/system logging print
```

### Serviços

```
/ip service print
/ip service set ssh disabled=no port=22
```

### Ferramentas de Diagnóstico

```
/ping ******* count=4
/tool traceroute *******
/tool bandwidth-test
/tool sniffer quick
```

## Comandos Avançados

### BGP

```
/routing bgp instance print
/routing bgp peer print
/routing bgp network print
```

### OSPF

```
/routing ospf instance print
/routing ospf network print
/routing ospf neighbor print
```

### VPN

```
/interface l2tp-server print
/interface pptp-server print
/interface sstp-server print
/interface ovpn-server print
```

### Queues e QoS

```
/queue simple print
/queue tree print
/queue type print
```

## Dicas para Uso Eficiente

1. **Prefixo de Comando**: Sempre use o prefixo `/` no início dos comandos.

2. **Comandos Completos**: Use comandos completos em vez de abreviações para maior compatibilidade.

3. **Paginação**: Para comandos que geram muita saída, o sistema detecta automaticamente prompts de paginação e envia espaço para continuar.

4. **Timeout**: O sistema está configurado com um timeout dinâmico que se ajusta à complexidade do comando.

5. **Comandos Multilinhas**: Evite comandos multilinhas complexos. Se necessário, divida em comandos individuais.

## Exemplos de Uso

### Verificar Status do Sistema

```
/system resource print
```

### Listar Interfaces de Rede

```
/interface print
```

### Verificar Configuração de IP

```
/ip address print
```

### Verificar Rotas

```
/ip route print
```

### Verificar Regras de Firewall

```
/ip firewall filter print
```

## Solução de Problemas

Se um comando não estiver funcionando como esperado:

1. Verifique se o comando está no formato correto com o prefixo `/`.
2. Tente usar a versão completa do comando (sem abreviações).
3. Verifique se o comando é compatível com a versão do RouterOS do dispositivo.
4. Para comandos que modificam a configuração, verifique se o usuário tem permissões adequadas.

## Referências

- [Documentação oficial do Mikrotik RouterOS](https://help.mikrotik.com/docs/display/ROS/RouterOS)
- [Wiki Mikrotik](https://wiki.mikrotik.com/wiki/Manual:TOC)
