import { FastifyInstance } from 'fastify'
import { UserController } from '../controllers/UserController'
import { verifyJWT } from '../middlewares/auth'
import { verifyAdmin } from '../middlewares/admin'
import { CreateUserDTO, UpdateUserDTO } from '../types/user'

const userController = new UserController()

export async function userRoutes(fastify: FastifyInstance) {
  // Rota pública para criação de usuário
  fastify.post<{ Body: CreateUserDTO }>('/users', async (request, reply) => {
    return userController.create(request, reply)
  })

  // Rotas protegidas que requerem autenticação
  fastify.register(async (fastify) => {
    // Aplica o middleware de autenticação
    fastify.addHook('onRequest', verifyJWT)

    // Rotas que requerem autenticação
    fastify.get('/users/profile', async (request, reply) => {
      const userId = request.user.sub
      const params = { id: userId }
      const req = { params } as any
      return userController.findById(req, reply)
    })

    // Rotas que requerem privilégios de administrador
    fastify.register(async (fastify) => {
      // Aplica o middleware de administrador
      fastify.addHook('onRequest', verifyAdmin)

      // Listar todos os usuários
      fastify.get('/users', async (request, reply) => {
        return userController.findAll(request, reply)
      })

      // Obter usuário por ID
      fastify.get('/users/:id', async (request, reply) => {
        return userController.findById(request, reply)
      })

      // Atualizar usuário
      fastify.put<{ Params: { id: string }, Body: UpdateUserDTO }>('/users/:id', async (request, reply) => {
        return userController.update(request, reply)
      })

      // Excluir usuário (agora faz delete lógico)
      fastify.delete<{ Params: { id: string } }>('/users/:id', async (request, reply) => {
        return userController.delete(request, reply)
      })

      // Desativar usuário
      fastify.patch<{ Params: { id: string } }>('/users/:id/deactivate', async (request, reply) => {
        return userController.deactivate(request, reply)
      })

      // Reativar usuário
      fastify.patch<{ Params: { id: string } }>('/users/:id/reactivate', async (request, reply) => {
        return userController.reactivate(request, reply)
      })
    })
  })
} 