export type ServerType = 'ssh'

export interface SSHServer {
  id: string
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  os: 'LINUX' | 'WINDOWS'
  deviceType: 'NOKIA' | 'HUAWEI' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  commands: Command[]
  userId: string
  createdAt: string
  updatedAt: string
  groupMembers?: {
    id: string
    groupId: string
    serverId: string
    group: {
      id: string
      name: string
      color?: string
    }
  }[]
}

export interface Command {
  id: string
  name: string
  command: string
  description?: string
  serverId: string
  order?: number
  createdAt: string
  updatedAt: string
}

export interface CommandResult {
  stdout?: string
  stderr?: string
  code?: number
}

export interface CreateServerDTO {
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  deviceType?: 'NOKIA' | 'HUAWEI' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  commands?: {
    name: string
    command: string
    description?: string
    order?: number
  }[]
}

export interface UpdateServerDTO {
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  deviceType?: 'NOKIA' | 'HUAW<PERSON>' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  commands: {
    id?: string
    name: string
    command: string
    description?: string
    order?: number
  }[]
}

export interface ExecuteCommandDTO {
  commandId: string
}

export interface CommandHistory {
  id: string
  userId: string
  serverId: string
  commandId: string
  commandName?: string
  commandText?: string
  result: string
  status: number
  executedAt: string
  user: {
    id: string
    name: string
    email: string
    active: boolean
  }
  server: {
    id: string
    name: string
    host: string
  }
  command?: {
    id: string
    name: string
    command: string
  }
}

export interface CommandTemplate {
  id: string
  name: string
  description?: string
  isPublic: boolean
  userId: string
  createdAt: string
  updatedAt: string
  commands: CommandTemplateItem[]
  user?: {
    id: string
    name: string
    email: string
  }
}

export interface CommandTemplateItem {
  id: string
  name: string
  command: string
  description?: string
  templateId: string
  order: number
}

export interface CreateCommandTemplateDTO {
  name: string
  description?: string
  isPublic: boolean
  commands: {
    name: string
    command: string
    description?: string
    order: number
  }[]
}

export interface UpdateCommandTemplateDTO {
  name: string
  description?: string
  isPublic: boolean
  commands: {
    id?: string
    name: string
    command: string
    description?: string
    order: number
  }[]
}

export interface Pagination {
  total: number
  totalPages: number
  currentPage: number
  limit: number
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: Pagination
}