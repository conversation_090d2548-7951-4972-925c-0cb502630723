import { Logger } from '../../utils/logger';

/**
 * Enum para tipos de dispositivos suportados
 */
export enum Device {
  NOKIA = 'NOKIA',
  HUAWEI = 'HUAWEI',
  MIKROTIK = 'MIKROTIK',
  DMOS = 'DMOS',
  GENERIC = 'GENERIC'
}

/**
 * Detecta o tipo de dispositivo com base no nome do servidor
 * @param name Nome do servidor
 * @returns Tipo de dispositivo detectado
 */
export function detectDevice(name: string): Device {
  const lowerName = name.toLowerCase();

  // Verificar se é um dispositivo Nokia
  if (lowerName.includes('nokia')) {
    Logger.log('Detectado dispositivo Nokia');
    return Device.NOKIA;
  }

  // Verificar se é um dispositivo Huawei/HarmonyOS
  if (lowerName.includes('harmony') || 
      lowerName.includes('huawei') || 
      lowerName.includes('rtr-pe-rbo')) {
    Logger.log('Detectado dispositivo Huawei');
    return Device.HUAWEI;
  }

  // Verificar se é um dispositivo Mikrotik
  if (lowerName.includes('mikrotik') || 
      lowerName.includes('routeros')) {
    Logger.log('Detectado dispositivo Mikrotik');
    return Device.MIKROTIK;
  }

  // Verificar se é um dispositivo DmOS
  if (lowerName.includes('dmos') || 
      lowerName.includes('datacom')) {
    Logger.log('Detectado dispositivo DmOS/Datacom');
    return Device.DMOS;
  }

  // Dispositivo genérico
  Logger.log('Dispositivo não reconhecido, usando configuração genérica');
  return Device.GENERIC;
}
