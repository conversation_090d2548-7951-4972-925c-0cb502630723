// Script para verificar a integridade do banco de dados
// Pode ser executado manualmente com: node scripts/verificar-integridade.js

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verificarIntegridade() {
  console.log('=== VERIFICAÇÃO DE INTEGRIDADE DO BANCO DE DADOS ===');
  console.log(`Data e hora: ${new Date().toLocaleString()}`);
  console.log(`URL do banco: ${process.env.DATABASE_URL.replace(/\/\/.*:.*@/, '//***:***@')}`);
  console.log('---------------------------------------------------');
  
  try {
    // Verificar se existem usuários
    const userCount = await prisma.user.count();
    console.log(`Usuários encontrados: ${userCount}`);
    
    if (userCount === 0) {
      console.error('❌ ALERTA CRÍTICO: Nenhum usuário encontrado no banco de dados!');
      console.error('   Isso pode indicar que o banco de dados foi limpo ou corrompido.');
      console.error('   Considere restaurar um backup recente.');
    } else {
      console.log('✅ Usuários OK');
      
      // Verificar se existe pelo menos um administrador
      const adminCount = await prisma.user.count({
        where: { role: 'ADMIN' }
      });
      
      if (adminCount === 0) {
        console.error('❌ ALERTA: Nenhum usuário administrador encontrado!');
      } else {
        console.log(`✅ Administradores OK (${adminCount} encontrados)`);
      }
      
      // Verificar se existe pelo menos um administrador ativo
      const activeAdminCount = await prisma.user.count({
        where: { 
          role: 'ADMIN',
          active: true
        }
      });
      
      if (activeAdminCount === 0) {
        console.error('❌ ALERTA: Nenhum administrador ativo encontrado!');
      } else {
        console.log(`✅ Administradores ativos OK (${activeAdminCount} encontrados)`);
      }
    }
    
    console.log('---------------------------------------------------');
    
    // Verificar se existem servidores
    const serverCount = await prisma.server.count();
    console.log(`Servidores encontrados: ${serverCount}`);
    
    if (serverCount === 0) {
      console.error('❌ ALERTA: Nenhum servidor encontrado no banco de dados!');
      console.error('   Isso pode indicar que o banco de dados foi limpo ou corrompido.');
      console.error('   Considere restaurar um backup recente.');
    } else {
      console.log('✅ Servidores OK');
    }
    
    console.log('---------------------------------------------------');
    
    // Verificar se existem comandos
    const commandCount = await prisma.command.count();
    console.log(`Comandos encontrados: ${commandCount}`);
    
    if (commandCount === 0) {
      console.error('❌ ALERTA: Nenhum comando encontrado no banco de dados!');
      console.error('   Isso pode indicar que os comandos foram apagados.');
      console.error('   Considere executar o script de atualização de comandos.');
    } else {
      console.log('✅ Comandos OK');
    }
    
    // Verificar se existem servidores sem comandos
    const serversWithoutCommands = await prisma.server.findMany({
      where: {
        commands: {
          none: {}
        }
      },
      select: {
        id: true,
        name: true
      }
    });
    
    if (serversWithoutCommands.length > 0) {
      console.error(`❌ ALERTA: Encontrados ${serversWithoutCommands.length} servidores sem comandos:`);
      serversWithoutCommands.forEach(server => {
        console.error(`   - ${server.name} (${server.id})`);
      });
      console.error('   Considere executar o script de atualização de comandos para esses servidores.');
    } else if (serverCount > 0) {
      console.log('✅ Todos os servidores possuem comandos');
    }
    
    console.log('---------------------------------------------------');
    
    // Verificar histórico de comandos
    const commandHistoryCount = await prisma.commandHistory.count();
    console.log(`Registros de histórico de comandos: ${commandHistoryCount}`);
    
    if (commandHistoryCount === 0 && serverCount > 0) {
      console.warn('⚠️ Nenhum histórico de comando encontrado, mas existem servidores.');
      console.warn('   Isso pode ser normal se nenhum comando foi executado ainda.');
    } else {
      console.log('✅ Histórico de comandos OK');
    }
    
    console.log('---------------------------------------------------');
    
    // Verificar templates de comandos
    const templateCount = await prisma.commandTemplate.count();
    console.log(`Templates de comandos: ${templateCount}`);
    
    // Verificar itens de templates
    const templateItemCount = await prisma.commandTemplateItem.count();
    console.log(`Itens de templates de comandos: ${templateItemCount}`);
    
    if (templateCount > 0 && templateItemCount === 0) {
      console.warn('⚠️ Existem templates de comandos, mas nenhum item de template.');
      console.warn('   Isso pode indicar um problema com os templates.');
    } else if (templateCount > 0) {
      console.log('✅ Templates de comandos OK');
    }
    
    console.log('---------------------------------------------------');
    console.log('Verificação de integridade concluída!');
    
  } catch (error) {
    console.error('❌ Erro ao verificar banco de dados:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verificarIntegridade();
