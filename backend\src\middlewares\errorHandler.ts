import { FastifyError, FastifyReply, FastifyRequest } from 'fastify'

export async function errorHandler(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
) {
  // Log detalhado usando o logger do Fastify
  request.log.error({
    msg: 'Erro na API',
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode: error.statusCode
    },
    request: {
      method: request.method,
      url: request.url,
      body: request.body,
      query: request.query,
      params: request.params
    }
  })

  // Resposta para o cliente
  const statusCode = error.statusCode || 500
  const message = error.message || 'Erro interno do servidor'

  return reply
    .status(statusCode)
    .send({
      error: message,
      statusCode,
      path: request.url,
      timestamp: new Date().toISOString()
    })
} 