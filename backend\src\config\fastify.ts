import fastify from 'fastify'
import cors from '@fastify/cors'
import jwt from '@fastify/jwt'
import { env } from './env'
import { errorHandler } from '../middlewares/errorHandler'

export const app = fastify({
  logger: {
    transport: {
      target: 'pino-pretty',
      options: {
        levelFirst: true,
        translateTime: 'HH:MM:ss',
        ignore: 'pid,hostname,reqId,responseTime,req,res',
        messageFormat: '{msg} {req.method} {req.url}'
      }
    }
  }
})

// Registra o handler de erro global
app.setErrorHandler(errorHandler)

app.register(cors, {
  origin: '*',
  credentials: true,
  methods: ['GET', 'PUT', 'POST', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization'],
})

app.register(jwt, {
  secret: env.JWT_SECRET,
})

app.decorate('authenticate', async function(request: any, reply: any) {
  try {
    await request.jwtVerify()
  } catch (err) {
    reply.send(err)
  }
}) 