import React, { createContext, useContext, useEffect, useState } from 'react'
import { api } from '../lib/axios'
import axios from 'axios'

interface User {
  id: string
  name: string
  email: string
  role: 'ADMIN' | 'USER'
}

interface AuthContextData {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => void
}

const AuthContext = createContext({} as AuthContextData)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  useEffect(() => {
    const token = localStorage.getItem('sem-fronteiras:token')

    if (token) {
      api.get('/me')
        .then(response => {
          setUser(response.data.user)
        })
        .catch(() => {
          localStorage.removeItem('sem-fronteiras:token')
        })
        .finally(() => {
          setIsLoading(false)
        })
    } else {
      setIsLoading(false)
    }
  }, [])

  async function signIn(email: string, password: string) {
    try {
      const response = await api.post('/login', {
        email,
        password,
      })

      const { token } = response.data

      localStorage.setItem('sem-fronteiras:token', token)

      const { data } = await api.get('/me')
      setUser(data.user)
    } catch (err) {
      localStorage.removeItem('sem-fronteiras:token')
      
      // Verificar se é um erro de usuário desativado (código 403)
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.status === 403) {
          throw new Error(err.response.data.message || 'Usuário desativado. Entre em contato com o administrador.')
        }
      }
      
      throw new Error('Credenciais inválidas')
    }
  }

  function signOut() {
    setUser(null)
    localStorage.removeItem('sem-fronteiras:token')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={{ user, isAuthenticated, isLoading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
} 