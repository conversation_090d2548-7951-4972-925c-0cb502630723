const { NodeSSH } = require('node-ssh');
const MikrotikExecutor = require('../src/services/ssh/executors/mikrotikExecutor').MikrotikExecutor;
const Logger = require('../src/utils/logger');

/**
 * Script para testar especificamente a correção do comando PING no Mikrotik
 * Valida se as estatísticas finais estão sendo capturadas corretamente
 */

async function testMikrotikPingFix() {
  Logger.log('🧪 === TESTE DE CORREÇÃO DO PING MIKROTIK ===');
  
  const ssh = new NodeSSH();
  
  try {
    // Conectar ao Mikrotik
    Logger.log('🔌 Conectando ao Mikrotik...');
    await ssh.connect({
      host: '***********',
      username: 'admin',
      password: '88701181Sem*',
      port: 22,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1'],
        compress: ['none']
      }
    });
    
    Logger.log('✅ Conectado com sucesso!');
    
    // Criar executor
    const executor = new MikrotikExecutor(ssh);
    
    // Testes de ping com diferentes alvos
    const pingTests = [
      {
        name: 'Ping simples Google DNS (*******)',
        command: 'ping *******',
        expectedPatterns: [
          'SEQ HOST',
          'sent=',
          'received=',
          'packet-loss='
        ]
      },
      {
        name: 'Ping com count específico (count=2)',
        command: '/ping address=******* count=2',
        expectedPatterns: [
          'SEQ HOST',
          'sent=2',
          'received=',
          'packet-loss='
        ]
      },
      {
        name: 'Ping Cloudflare DNS (*******)',
        command: 'ping *******',
        expectedPatterns: [
          'SEQ HOST',
          'sent=',
          'received=',
          'packet-loss='
        ]
      }
    ];
    
    for (let i = 0; i < pingTests.length; i++) {
      const test = pingTests[i];
      Logger.log(`\n🎯 === TESTE ${i + 1}: ${test.name} ===`);
      Logger.log(`📝 Comando: ${test.command}`);
      
      try {
        const startTime = Date.now();
        
        // Executar o comando ping
        Logger.log('🚀 Executando comando ping...');
        const result = await executor.executeCommand(test.command);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        Logger.log(`⏱️ Duração: ${duration}ms`);
        Logger.log(`📊 Código de saída: ${result.code}`);
        
        // Analisar a saída
        if (result.stdout) {
          Logger.log('\n📤 === SAÍDA COMPLETA ===');
          console.log(result.stdout);
          Logger.log('=== FIM DA SAÍDA ===\n');
          
          // Verificar se todos os padrões esperados estão presentes
          Logger.log('🔍 Verificando padrões esperados:');
          let allPatternsFound = true;
          
          for (const pattern of test.expectedPatterns) {
            const found = result.stdout.includes(pattern);
            Logger.log(`  ${found ? '✅' : '❌'} ${pattern}: ${found ? 'ENCONTRADO' : 'NÃO ENCONTRADO'}`);
            if (!found) allPatternsFound = false;
          }
          
          // Verificar estatísticas específicas
          const hasStatistics = result.stdout.includes('sent=') && 
                               result.stdout.includes('received=') && 
                               result.stdout.includes('packet-loss=');
          
          Logger.log(`\n📊 Estatísticas finais: ${hasStatistics ? '✅ PRESENTES' : '❌ AUSENTES'}`);
          
          // Verificar se há dados de cada pacote
          const hasPacketData = result.stdout.includes('SEQ HOST') || 
                               result.stdout.match(/\d+\s+\d+\.\d+\.\d+\.\d+/);
          
          Logger.log(`📦 Dados dos pacotes: ${hasPacketData ? '✅ PRESENTES' : '❌ AUSENTES'}`);
          
          // Resultado final do teste
          if (result.code === 0 && allPatternsFound && hasStatistics) {
            Logger.log('🎉 TESTE PASSOU - Ping funcionando corretamente!');
          } else {
            Logger.error('❌ TESTE FALHOU - Problemas detectados na saída do ping');
          }
          
        } else {
          Logger.error('❌ TESTE FALHOU - Nenhuma saída recebida');
        }
        
        if (result.stderr) {
          Logger.warn('⚠️ Erros detectados:');
          console.log(result.stderr);
        }
        
      } catch (error) {
        Logger.error(`❌ TESTE FALHOU - Erro: ${error.message}`);
      }
      
      // Aguardar entre testes
      if (i < pingTests.length - 1) {
        Logger.log('⏳ Aguardando 5 segundos antes do próximo teste...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
  } catch (error) {
    Logger.error('❌ Erro na conexão:', error);
  } finally {
    // Fechar conexão
    if (ssh.isConnected()) {
      ssh.dispose();
      Logger.log('🔌 Conexão SSH fechada');
    }
  }
  
  Logger.log('\n🏁 === TESTE DE CORREÇÃO DO PING CONCLUÍDO ===');
}

// Executar o teste
if (require.main === module) {
  testMikrotikPingFix().catch(console.error);
}

module.exports = { testMikrotikPingFix };
