import { FastifyRequest, FastifyReply } from 'fastify'
import { Role } from '@prisma/client'

export async function verifyAdmin(request: FastifyRequest, reply: FastifyReply) {
  try {
    if (request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: '<PERSON>sso negado. Requer privilégios de administrador.' })
    }
  } catch (error) {
    return reply.status(500).send({ error: 'Erro ao verificar permissões de administrador' })
  }
} 