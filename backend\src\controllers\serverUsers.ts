import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient, Role } from '@prisma/client'
import { ServerUserDTO } from '../types/server'

const prisma = new PrismaClient()

// Listar usuários com acesso a um servidor específico
export async function listServerUsers(
  request: FastifyRequest<{ Params: { serverId: string } }>,
  reply: FastifyReply,
) {
  try {
    const { serverId } = request.params

    // Verificar se o usuário é administrador ou dono do servidor
    const server = await prisma.server.findUnique({
      where: { id: serverId },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Apenas administradores ou o dono do servidor podem ver os usuários com acesso
    if (request.user.role !== Role.ADMIN && server.userId !== request.user.id) {
      return reply.status(403).send({ error: '<PERSON><PERSON> negado' })
    }

    const serverUsers = await prisma.serverUser.findMany({
      where: { serverId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            active: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return reply.send(serverUsers)
  } catch (error) {
    console.error('Erro ao listar usuários do servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Adicionar acesso de usuário a um servidor
export async function addServerUser(
  request: FastifyRequest,
  reply: FastifyReply,
) {
  try {
    const { userId, serverId } = request.body as ServerUserDTO

    // Verificar se o usuário é administrador
    if (request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Apenas administradores podem atribuir usuários a servidores' })
    }

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id: serverId },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário existe
    const user = await prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      return reply.status(404).send({ error: 'Usuário não encontrado' })
    }

    // Verificar se o acesso já existe
    const existingAccess = await prisma.serverUser.findFirst({
      where: {
        userId,
        serverId,
      },
    })

    if (existingAccess) {
      return reply.status(400).send({ error: 'Usuário já tem acesso a este servidor' })
    }

    // Adicionar acesso
    const serverUser = await prisma.serverUser.create({
      data: {
        userId,
        serverId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            active: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return reply.status(201).send(serverUser)
  } catch (error) {
    console.error('Erro ao adicionar acesso de usuário ao servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Remover acesso de usuário a um servidor
export async function removeServerUser(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    // Verificar se o acesso existe
    const serverUser = await prisma.serverUser.findUnique({
      where: { id },
      include: {
        server: true,
      },
    })

    if (!serverUser) {
      return reply.status(404).send({ error: 'Acesso não encontrado' })
    }

    // Verificar se o usuário é administrador ou dono do servidor
    if (request.user.role !== Role.ADMIN && serverUser.server.userId !== request.user.id) {
      return reply.status(403).send({ error: 'Acesso negado' })
    }

    // Remover acesso
    await prisma.serverUser.delete({
      where: { id },
    })

    return reply.send({ message: 'Acesso removido com sucesso' })
  } catch (error) {
    console.error('Erro ao remover acesso de usuário ao servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
} 