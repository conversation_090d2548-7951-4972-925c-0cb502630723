import { Fragment, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { updateServer } from '../services/api'
import { Server, Globe, User, Key, Terminal, Plus, Trash2, X } from 'lucide-react'
import { CommandInput } from './CommandInput'
import { ScriptFileUpload } from './ScriptFileUpload'
import { SSHServer, Command } from '../types/server'

const serverSchema = z.object({
  name: z.string().min(3, 'O nome deve ter no mínimo 3 caracteres'),
  host: z.string().min(1, 'O host é obrigatório'),
  port: z.number().min(1, 'A porta é obrigatória'),
  username: z.string().min(1, 'O usuário é obrigatório'),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  deviceType: z.enum(['NOKIA', 'HUAWEI', 'MIKROTIK', 'DMOS', 'GENERIC']).default('GENERIC'),
  commands: z.array(z.object({
    name: z.string().min(1, 'O nome do comando é obrigatório'),
    command: z.string().min(1, 'O comando é obrigatório'),
    description: z.string().optional(),
    order: z.number().optional(),
  })).optional().default([]),
})

type ServerFormData = z.infer<typeof serverSchema>

interface EditServerModalProps {
  isOpen: boolean
  onClose: () => void
  server: SSHServer & { commands: Command[] }
  onServerUpdated: () => void
}

export default function EditServerModal({
  isOpen,
  onClose,
  server,
  onServerUpdated,
}: EditServerModalProps) {
  const { register, handleSubmit, control, formState: { errors, isSubmitting } } = useForm<ServerFormData>({
    resolver: zodResolver(serverSchema),
    defaultValues: {
      name: server.name,
      host: server.host,
      port: server.port,
      username: server.username,
      password: server.password || '',
      privateKey: server.privateKey || '',
      deviceType: server.deviceType || 'GENERIC',
      commands: server.commands.map((cmd, index) => ({
        ...cmd,
        order: cmd.order !== undefined ? cmd.order : index
      })) || [],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'commands',
  })

  // Efeito para adicionar atalho de teclado para salvar
  useEffect(() => {
    // Adicionar um evento de teclado global para o botão de salvar
    const handleKeyDown = (e: KeyboardEvent) => {
      // Se o usuário pressionar Ctrl+S ou Cmd+S
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();

        // Obter os valores atuais do formulário
        const formValues = control._formValues;

        // Chamar a função onSubmit diretamente com os valores do formulário
        onSubmit(formValues as ServerFormData);
      }
    };

    // Adicionar o evento
    window.addEventListener('keydown', handleKeyDown);

    // Remover o evento quando o componente for desmontado
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);


  async function onSubmit(data: ServerFormData) {
    try {
      // Verificar se os dados são válidos
      if (!data.name || !data.host || !data.port || !data.username) {
        alert('Por favor, preencha todos os campos obrigatórios.');
        return;
      }

      // Garantir que todos os comandos tenham uma ordem definida
      // Importante: Usar o índice atual como ordem para garantir que a ordem visual seja preservada
      const commandsWithOrder = data.commands?.map((cmd, index) => ({
        ...cmd,
        id: (cmd as any).id, // Preservar o ID se existir
        order: index // Usar o índice atual como ordem, ignorando o valor anterior
      })) || [];

      // Filtrar comandos vazios
      const filteredCommands = commandsWithOrder.filter(cmd => cmd.name && cmd.command);

      const updateData = {
        name: data.name,
        host: data.host,
        port: data.port,
        username: data.username,
        password: data.password || '',
        privateKey: data.privateKey || '',
        deviceType: data.deviceType || 'GENERIC',
        commands: filteredCommands,
      }


      // Usar try/catch específico para a chamada da API
      try {
        await updateServer(server.id, updateData);
        onServerUpdated();
        onClose();
      } catch (apiError: any) {
        console.error('Erro na chamada da API:', apiError);
        alert(`Erro ao atualizar o servidor: ${apiError.message || 'Erro desconhecido'}`);
      }
    } catch (error) {
      console.error('Erro ao atualizar servidor:', error);
      alert('Erro ao salvar o servidor. Verifique o console para mais detalhes.');
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Editar Servidor
                  </h3>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <form
                  onSubmit={(e) => {
                    handleSubmit(onSubmit)(e);
                  }}
                  className="space-y-6"
                >
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Nome do Servidor
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Server className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          {...register('name')}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="host" className="block text-sm font-medium text-gray-700">
                        Host
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Globe className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          {...register('host')}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      {errors.host && (
                        <p className="mt-1 text-sm text-red-600">{errors.host.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="port" className="block text-sm font-medium text-gray-700">
                        Porta
                      </label>
                      <div className="mt-1">
                        <input
                          type="number"
                          {...register('port', { valueAsNumber: true })}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      {errors.port && (
                        <p className="mt-1 text-sm text-red-600">{errors.port.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                        Usuário
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          {...register('username')}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      {errors.username && (
                        <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Senha (opcional)
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Key className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="password"
                          {...register('password')}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="deviceType" className="block text-sm font-medium text-gray-700">
                        Tipo de Dispositivo
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Server className="h-5 w-5 text-gray-400" />
                        </div>
                        <select
                          id="deviceType"
                          {...register('deviceType')}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="GENERIC">Genérico</option>
                          <option value="NOKIA">Nokia</option>
                          <option value="HUAWEI">Huawei</option>
                          <option value="MIKROTIK">Mikrotik</option>
                          <option value="DMOS">DmOS/Datacom</option>
                        </select>
                      </div>
                      {errors.deviceType && (
                        <p className="mt-1 text-sm text-red-600">{errors.deviceType.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-4">
                        Comandos
                      </label>
                      <div className="grid grid-cols-2 gap-4">
                        {fields.map((field, index) => (
                          <div
                            key={field.id}
                            className="space-y-2 border border-gray-200 p-3 pt-10 rounded-md relative"
                          >
                            <div className="w-full">
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <Terminal className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="text"
                                  {...register(`commands.${index}.name`)}
                                  placeholder="Nome do Comando"
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                />
                              </div>
                              {errors.commands?.[index]?.name && (
                                <p className="mt-1 text-sm text-red-600">
                                  {errors.commands[index]?.name?.message}
                                </p>
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="space-y-2">
                                <CommandInput
                                  name={`commands.${index}.command`}
                                  register={register}
                                  placeholder="Digite ou cole seus comandos aqui"
                                  error={errors.commands?.[index]?.command?.message}
                                  defaultValue={fields[index].command || ''}
                                />
                                <div className="flex justify-between items-center mt-1">
                                  <ScriptFileUpload
                                    onFileContent={(content) => {
                                      // Atualiza o valor no formulário
                                      const field = register(`commands.${index}.command`)
                                      field.onChange({
                                        target: {
                                          name: `commands.${index}.command`,
                                          value: content
                                        }
                                      } as any)

                                      // Atualiza diretamente o textarea para visualização imediata
                                      const textarea = document.querySelector(`textarea[name="commands.${index}.command"]`) as HTMLTextAreaElement
                                      if (textarea) {
                                        textarea.value = content
                                        // Dispara evento de input para acionar o auto-resize
                                        textarea.dispatchEvent(new Event('input', { bubbles: true }))
                                      }
                                    }}
                                  />
                                  <button
                                    type="button"
                                    onClick={() => remove(index)}
                                    className="p-2 text-gray-400 hover:text-red-500"
                                  >
                                    <Trash2 className="h-5 w-5" />
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Campo oculto para armazenar a ordem */}
                            <input
                              type="hidden"
                              {...register(`commands.${index}.order`)}
                              defaultValue={index}
                              value={index}
                            />
                          </div>
                        ))}
                      </div>

                      <button
                        type="button"
                        onClick={() => append({ name: '', command: '', description: '', order: fields.length })}
                        className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <Plus className="h-5 w-5 mr-2" />
                        Adicionar Comando
                      </button>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end gap-2">
                    <button
                      type="button"
                      disabled={isSubmitting}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      onClick={async () => {
                        try {
                          // Obter os valores atuais do formulário
                          const formValues = control._formValues;

                          // Chamar a função onSubmit diretamente com os valores do formulário
                          await onSubmit(formValues as ServerFormData);
                        } catch (error) {
                          console.error('Erro ao salvar:', error);
                          alert('Erro ao salvar o servidor. Verifique o console para mais detalhes.');
                        }
                      }}
                    >
                      {isSubmitting ? 'Salvando...' : 'Salvar'}
                    </button>
                    <button
                      type="button"
                      disabled={isSubmitting}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      onClick={() => onClose()}
                    >
                      Cancelar
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}