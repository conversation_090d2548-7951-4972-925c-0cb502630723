/**
 * Script para monitorar o uso de memória do Node.js e reiniciar o processo se necessário
 * 
 * Este script deve ser executado como um processo separado que monitora o servidor Node.js
 * Ele verifica periodicamente o uso de memória e reinicia o servidor se ultrapassar um limite
 */

const http = require('http');
const { exec } = require('child_process');
const fs = require('fs');

// Configurações
const HEALTH_CHECK_URL = 'http://localhost:3000/health';
const MEMORY_THRESHOLD_MB = 1500; // 1.5GB
const CHECK_INTERVAL_MS = 30000; // 30 segundos
const LOG_FILE = './memory-monitor.log';

// Função para registrar logs
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  fs.appendFileSync(LOG_FILE, logMessage);
}

// Função para verificar a saúde do servidor
function checkServerHealth() {
  return new Promise((resolve, reject) => {
    http.get(HEALTH_CHECK_URL, (res) => {
      if (res.statusCode !== 200) {
        return reject(new Error(`Status code: ${res.statusCode}`));
      }
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const healthData = JSON.parse(data);
          resolve(healthData);
        } catch (error) {
          reject(new Error(`Falha ao analisar resposta: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

// Função para reiniciar o servidor
function restartServer() {
  return new Promise((resolve, reject) => {
    log('Reiniciando o servidor Node.js...');
    
    // Comando para reiniciar o container Docker
    exec('docker restart sem-fronteiras_backend_1', (error, stdout, stderr) => {
      if (error) {
        log(`Erro ao reiniciar servidor: ${error.message}`);
        return reject(error);
      }
      
      log('Servidor reiniciado com sucesso');
      log(`stdout: ${stdout}`);
      
      if (stderr) {
        log(`stderr: ${stderr}`);
      }
      
      resolve();
    });
  });
}

// Função principal de monitoramento
async function monitorServer() {
  try {
    log('Verificando saúde do servidor...');
    const healthData = await checkServerHealth();
    
    // Extrair uso de memória
    const heapUsedStr = healthData.memory.heapUsed;
    const heapUsedMB = parseInt(heapUsedStr.replace('MB', ''));
    
    log(`Uso de memória: ${heapUsedMB}MB / ${MEMORY_THRESHOLD_MB}MB (limite)`);
    
    // Verificar se ultrapassou o limite
    if (heapUsedMB > MEMORY_THRESHOLD_MB) {
      log(`ALERTA: Uso de memória (${heapUsedMB}MB) excedeu o limite (${MEMORY_THRESHOLD_MB}MB)`);
      await restartServer();
    }
  } catch (error) {
    log(`Erro ao verificar servidor: ${error.message}`);
    
    // Se não conseguir conectar, pode ser que o servidor esteja travado
    log('Servidor pode estar inacessível, tentando reiniciar...');
    await restartServer();
  }
}

// Iniciar monitoramento
log('Iniciando monitoramento de memória do servidor Node.js');
log(`Limite de memória configurado: ${MEMORY_THRESHOLD_MB}MB`);
log(`Intervalo de verificação: ${CHECK_INTERVAL_MS}ms`);

// Executar verificação inicial
monitorServer();

// Configurar verificação periódica
setInterval(monitorServer, CHECK_INTERVAL_MS);
