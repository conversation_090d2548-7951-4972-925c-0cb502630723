const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixCommandHistory() {
  try {
    console.log('Verificando a estrutura da tabela CommandHistory...');
    
    // Verificar se a coluna createdAt existe
    const columnInfo = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'CommandHistory' AND column_name = 'createdAt';
    `;
    
    console.log('Informações da coluna createdAt:', columnInfo);
    
    if (columnInfo.length === 0) {
      console.log('A coluna createdAt não existe. Adicionando...');
      
      // Adicionar a coluna createdAt
      await prisma.$executeRaw`
        ALTER TABLE "CommandHistory" 
        ADD COLUMN "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
      `;
      
      console.log('<PERSON>una createdAt adicionada com sucesso!');
    } else {
      console.log('A coluna createdAt já existe na tabela.');
    }
    
    // Verificar se há registros sem createdAt definido
    const recordsWithoutCreatedAt = await prisma.$queryRaw`
      SELECT COUNT(*) FROM "CommandHistory" 
      WHERE "createdAt" IS NULL;
    `;
    
    console.log('Registros sem createdAt:', recordsWithoutCreatedAt);
    
    if (recordsWithoutCreatedAt[0].count > 0) {
      console.log('Atualizando registros sem createdAt...');
      
      // Atualizar registros sem createdAt para usar executedAt
      await prisma.$executeRaw`
        UPDATE "CommandHistory" 
        SET "createdAt" = "executedAt" 
        WHERE "createdAt" IS NULL;
      `;
      
      console.log('Registros atualizados com sucesso!');
    }
    
    console.log('Verificação e correção concluídas com sucesso!');
    
  } catch (error) {
    console.error('Erro ao corrigir a tabela CommandHistory:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixCommandHistory();
