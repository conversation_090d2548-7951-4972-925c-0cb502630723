import { NodeSSH } from 'node-ssh';
import { RouterOSAPI } from 'node-routeros-v2';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos Mikrotik
 * Implementação otimizada que prioriza SSH com configurações robustas
 * e fallback para API nativa quando necessário
 */
export class MikrotikExecutor extends BaseExecutor {
  private routerOsApi: RouterOSAPI | null = null;
  private apiConnected: boolean = false;
  private host: string = '';
  private username: string = '';
  private password: string = '';
  private port: number = 8728; // Porta padrão da API Mikrotik
  private connectionAttempts: number = 0;
  private maxConnectionAttempts: number = 2; // Reduzido para 2 tentativas
  private keepaliveInterval: NodeJS.Timeout | null = null;
  private lastCommandTime: number = 0;

  constructor(ssh: NodeSSH) {
    super(ssh);

    // Configurar timeouts otimizados para Mikrotik
    this.BASE_TIMEOUT = 45000; // 45 segundos como base (reduzido)
    this.TIMEOUT_PER_COMMAND = 8000; // 8 segundos adicionais por comando (reduzido)
    this.MAX_TIMEOUT = 120000; // Limite máximo de 2 minutos (reduzido)

    // Extrair informações de conexão do SSH para usar na API
    if (ssh.connection) {
      const config = ssh.connection.config;
      this.host = config.host || '';
      this.username = config.username || '';
      this.password = config.password || '';

      // Inicializar keepalive manual para Mikrotik
      this.startKeepalive();
    }

    // Configurar handler para limpar recursos quando o SSH for encerrado
    ssh.connection?.on('close', () => {
      this.cleanup();
    });

    ssh.connection?.on('error', (error: any) => {
      Logger.error('Erro na conexão SSH Mikrotik:', error);
      this.cleanup();
    });
  }

  /**
   * Inicia o keepalive manual para manter a conexão SSH ativa
   */
  private startKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
    }

    Logger.log('Iniciando keepalive manual para Mikrotik a cada 30 segundos');

    this.keepaliveInterval = setInterval(async () => {
      try {
        // Verificar se passou tempo suficiente desde o último comando
        const timeSinceLastCommand = Date.now() - this.lastCommandTime;

        // Só enviar keepalive se não houve comando recente (últimos 25 segundos)
        if (timeSinceLastCommand > 25000) {
          // Enviar comando simples para manter a conexão ativa
          await this.ssh.execCommand('system identity print', {
            execOptions: { pty: false } // Sem PTY para keepalive
          });
          Logger.log('Keepalive enviado para Mikrotik');
        }
      } catch (error) {
        // Ignorar erros de keepalive silenciosamente
        // Logger.log('Keepalive falhou (normal):', error);
      }
    }, 30000); // A cada 30 segundos
  }

  /**
   * Para o keepalive manual
   */
  private stopKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
      this.keepaliveInterval = null;
      Logger.log('Parando keepalive manual para Mikrotik');
    }
  }

  /**
   * Limpa todos os recursos
   */
  private cleanup(): void {
    this.stopKeepalive();
    this.closeApi();
  }

  /**
   * Fecha a conexão com a API do RouterOS
   */
  public async closeApi(): Promise<void> {
    if (this.routerOsApi && this.apiConnected) {
      try {
        Logger.log(`Fechando conexão API Mikrotik para ${this.host}`);
        await this.routerOsApi.close();
        this.apiConnected = false;
      } catch (error) {
        Logger.error('Erro ao fechar conexão API Mikrotik:', error);
      }
    }
    this.routerOsApi = null;
  }

  /**
   * Inicializa a API do RouterOS (desabilitada por padrão)
   */
  private initializeApi(): void {
    // API desabilitada por padrão para priorizar SSH
    // Pode ser habilitada se necessário
    Logger.log('API Mikrotik desabilitada, usando apenas SSH');
    return;

    /*
    try {
      this.routerOsApi = new RouterOSAPI({
        host: this.host,
        user: this.username,
        password: this.password,
        port: this.port,
        keepalive: true,
        timeout: 20000
      });

      Logger.log(`API Mikrotik inicializada para ${this.host}`);
    } catch (error) {
      Logger.error('Erro ao inicializar API Mikrotik:', error);
      this.routerOsApi = null;
    }
    */
  }

  /**
   * Conecta à API do RouterOS (desabilitada por padrão)
   */
  private async connectApi(): Promise<boolean> {
    // API desabilitada por padrão para priorizar SSH
    return false;

    /*
    if (!this.routerOsApi) {
      this.initializeApi();
      if (!this.routerOsApi) {
        return false;
      }
    }

    try {
      if (this.apiConnected) {
        // Verificar se a conexão ainda está ativa
        try {
          // Teste simples para verificar se a conexão está ativa
          await this.routerOsApi.write('/system/identity/print');
          return true;
        } catch (error) {
          Logger.log('Conexão API Mikrotik perdida, reconectando...');
          this.apiConnected = false;
        }
      }

      this.connectionAttempts++;
      Logger.log(`Conectando à API Mikrotik (${this.host}) - Tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}`);

      // Criar uma promise com timeout de 3 segundos
      const connectPromise = this.routerOsApi.connect();
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => {
          reject(new Error('API_TIMEOUT'));
        }, 3000); // 3 segundos
      });

      // Executar com timeout
      await Promise.race([connectPromise, timeoutPromise]);

      this.apiConnected = true;
      this.connectionAttempts = 0;

      Logger.log(`Conexão API Mikrotik estabelecida com sucesso: ${this.host}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage === 'API_TIMEOUT') {
        Logger.error(`Timeout ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
      } else {
        Logger.error(`Falha ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}):`, error);
      }

      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        this.connectionAttempts = 0;
        Logger.log('Número máximo de tentativas de conexão API Mikrotik excedido, usando SSH como fallback');
        return false;
      }

      return false;
    }
    */
  }

  /**
   * Verifica se um comando é interativo e precisa de PTY
   * @param command Comando a ser verificado
   * @returns true se o comando é interativo
   */
  private isInteractiveCommand(command: string): boolean {
    // Deixe apenas comandos que realmente exigem fluxo contínuo
    const interactiveCommands = [
      'tool bandwidth-test',
      'tool sniffer',
      'tool torch'
    ];

    const cleanCommand = command.toLowerCase().trim();
    return interactiveCommands.some(cmd => cleanCommand.includes(cmd));
  }

  /**
   * Formata comandos de diagnóstico para a sintaxe correta do Mikrotik
   * @param command Comando original
   * @returns Comando formatado
   */
  private formatDiagnosticCommand(command: string): string {
    const cleanCommand = command.trim().toLowerCase();

    // Se o comando já está na sintaxe correta do Mikrotik, retornar como está
    if (command.startsWith('/ping address=') ||
        command.startsWith('/tool traceroute address=') ||
        command.startsWith('/tool ping address=')) {
      return command.trim();
    }

    // CORREÇÃO CRÍTICA: Detectar comandos que já contêm address= mas não começam com /
    if (cleanCommand.startsWith('ping address=') || cleanCommand.startsWith('tool ping address=')) {
      // Apenas adicionar o prefixo / sem duplicar address=
      return `/${command.trim()}`;
    }

    if (cleanCommand.startsWith('tool traceroute address=') || cleanCommand.startsWith('traceroute address=')) {
      // Apenas adicionar o prefixo / sem duplicar address=
      return `/${command.trim()}`;
    }

    // Detectar comandos ping simples (sem address=) e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('ping ') || cleanCommand === 'ping') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];

        // Verificar se já contém parâmetros count, interval, etc.
        const hasCount = command.toLowerCase().includes('count=');
        const hasInterval = command.toLowerCase().includes('interval=');
        const hasSize = command.toLowerCase().includes('size=');

        // Construir comando preservando parâmetros existentes
        let pingCommand = `/ping address=${target}`;

        // Extrair e preservar parâmetros existentes
        if (hasCount) {
          const countMatch = command.match(/count=(\d+)/i);
          if (countMatch) {
            pingCommand += ` count=${countMatch[1]}`;
          }
        } else {
          // Adicionar count padrão apenas se não existir
          pingCommand += ' count=4';
        }

        if (hasInterval) {
          const intervalMatch = command.match(/interval=([^\s]+)/i);
          if (intervalMatch) {
            pingCommand += ` interval=${intervalMatch[1]}`;
          }
        }

        if (hasSize) {
          const sizeMatch = command.match(/size=(\d+)/i);
          if (sizeMatch) {
            pingCommand += ` size=${sizeMatch[1]}`;
          }
        }

        Logger.log(`Comando ping convertido: "${command}" → "${pingCommand}"`);
        return pingCommand;
      }
      return '/ping';
    }

    // Detectar comandos traceroute simples (sem address=) e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('traceroute ') || cleanCommand === 'traceroute') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];
        // Usar sintaxe /tool traceroute com configurações válidas para Mikrotik
        return `/tool traceroute address=${target} count=1 max-hops=30`;
      }
      return '/tool traceroute count=1 max-hops=30';
    }

    // Detectar comandos tool traceroute e adicionar limites se não existirem
    if (cleanCommand.startsWith('tool traceroute')) {
      const hasMaxHops = command.includes('max-hops=');
      const hasCount = command.includes('count=');

      let formattedCommand = command.trim();

      // Adicionar count se não existir
      if (!hasCount) {
        formattedCommand += ' count=1';
      }

      // Adicionar max-hops se não existir
      if (!hasMaxHops) {
        formattedCommand += ' max-hops=30';
      }

      // Remover timeout se existir (pode causar erro no Mikrotik)
      formattedCommand = formattedCommand.replace(/\s+timeout=[^\s]+/g, '');

      return formattedCommand.startsWith('/') ? formattedCommand : `/${formattedCommand}`;
    }

    // Para comandos que já começam com /, manter como estão
    if (command.startsWith('/')) {
      return command.trim();
    }

    // Adicionar / no início para outros comandos
    return `/${command.trim()}`;
  }

  /**
   * Executa comandos de diagnóstico (ping/traceroute) com timeout interno estendido
   * @param cmd Comando sem barra inicial
   * @param isPing Se é comando ping
   * @param isTrace Se é comando traceroute
   * @returns Resultado do comando
   */
  private async executeDiagnosticCommand(
    cmd: string,
    isPing: boolean,
    isTrace: boolean
  ): Promise<CommandResult> {
    // Para traceroute, usar monitoramento inteligente; para ping, usar timeout simples
    if (isTrace) {
      return await this.executeTracerouteWithSmartMonitoring(cmd);
    } else if (isPing) {
      return await this.executePingWithTimeout(cmd);
    } else {
      throw new Error('Comando de diagnóstico não reconhecido');
    }
  }

  /**
   * Executa ping com timeout simples
   */
  private async executePingWithTimeout(cmd: string): Promise<CommandResult> {
    const pingTimeout = 25000; // 25s para ping

    Logger.log(`Executando comando PING com timeout de ${pingTimeout}ms`);

    const sshOptions = {
      execOptions: { pty: false, env: {} },
      timeout: pingTimeout,
      onStdout: (chunk: any) => {
        if (chunk.length > 0) {
          Logger.log(`stdout Mikrotik PING: ${chunk.length} bytes - "${chunk.toString().substring(0, 100)}${chunk.length > 100 ? '...' : ''}"`);
        }
      },
      onStderr: (chunk: any) => {
        if (chunk.length > 0) {
          Logger.error(`stderr Mikrotik: ${chunk.length} bytes recebidos`);
        }
      }
    };

    const result = await this.ssh.execCommand(cmd, sshOptions);
    const cleanStdout = this.cleanAnsiOutput(result.stdout);
    const cleanStderr = this.cleanAnsiOutput(result.stderr);

    this.analyzeDiagnosticOutput(cleanStdout, true, false);

    return {
      stdout: this.formatMikrotikOutput(cleanStdout),
      stderr: cleanStderr,
      code: result.code || 0
    };
  }

  /**
   * Executa traceroute com monitoramento inteligente de progresso
   */
  private async executeTracerouteWithSmartMonitoring(cmd: string): Promise<CommandResult> {
    Logger.log('Executando comando TRACEROUTE com monitoramento inteligente de progresso');

    return new Promise(async (resolve, reject) => {
      let completed = false;
      let accumulatedOutput = '';
      let lastProgressTime = Date.now();
      let lastHopCount = 0;
      let stagnationCount = 0;

      const maxStagnationTime = 25000; // 25 segundos sem progresso (aumentado)
      const maxTotalTime = 60000; // 60 segundos máximo total (aumentado)
      const progressCheckInterval = 5000; // Verificar progresso a cada 5 segundos (aumentado)

      // Timeout absoluto
      const absoluteTimeout = setTimeout(() => {
        if (!completed) {
          completed = true;
          Logger.log('TRACEROUTE - Timeout absoluto atingido (60s)');
          resolve({
            stdout: accumulatedOutput || '[TIMEOUT] Traceroute não completou dentro do tempo limite',
            stderr: '',
            code: 124
          });
        }
      }, maxTotalTime);

      // Monitoramento de progresso
      const progressMonitor = setInterval(() => {
        if (completed) {
          clearInterval(progressMonitor);
          return;
        }

        const currentHopCount = this.countHopsInOutput(accumulatedOutput);
        const timeSinceLastProgress = Date.now() - lastProgressTime;

        Logger.log(`TRACEROUTE - Progresso: ${currentHopCount} hops, última atividade há ${timeSinceLastProgress}ms`);

        // Verificar se houve progresso
        if (currentHopCount > lastHopCount) {
          lastHopCount = currentHopCount;
          lastProgressTime = Date.now();
          stagnationCount = 0;
          Logger.log(`TRACEROUTE - Progresso detectado: novo hop ${currentHopCount}`);
        } else {
          stagnationCount++;
          Logger.log(`TRACEROUTE - Sem progresso: contagem de estagnação ${stagnationCount}`);
        }

        // Verificar se deve finalizar por falta de progresso - critérios mais rigorosos
        if (timeSinceLastProgress > maxStagnationTime || stagnationCount >= 8) {
          completed = true;
          clearInterval(progressMonitor);
          clearTimeout(absoluteTimeout);

          Logger.log(`TRACEROUTE - Finalizando por falta de progresso (${timeSinceLastProgress}ms sem atividade)`);

          // Verificar se temos dados suficientes - critério mais rigoroso
          if (this.isTracerouteComplete(accumulatedOutput) || currentHopCount >= 5) {
            resolve({
              stdout: this.formatMikrotikOutput(accumulatedOutput),
              stderr: '',
              code: 0
            });
          } else {
            resolve({
              stdout: accumulatedOutput || '[SEM PROGRESSO] Traceroute parou de progredir',
              stderr: '',
              code: 124
            });
          }
        }
      }, progressCheckInterval);

      try {
        const sshOptions = {
          execOptions: { pty: false, env: {} },
          timeout: maxTotalTime + 5000,
          onStdout: (chunk: any) => {
            if (chunk.length > 0 && !completed) {
              const chunkStr = chunk.toString();
              accumulatedOutput += chunkStr;
              lastProgressTime = Date.now();

              Logger.log(`stdout Mikrotik TRACEROUTE: ${chunk.length} bytes - "${chunkStr.substring(0, 100)}${chunkStr.length > 100 ? '...' : ''}"`);

              // Verificar se completou durante a execução
              if (this.isTracerouteComplete(accumulatedOutput)) {
                completed = true;
                clearInterval(progressMonitor);
                clearTimeout(absoluteTimeout);

                Logger.log('TRACEROUTE - Completado durante execução (padrão de fim detectado)');
                resolve({
                  stdout: this.formatMikrotikOutput(accumulatedOutput),
                  stderr: '',
                  code: 0
                });
              }
            }
          },
          onStderr: (chunk: any) => {
            if (chunk.length > 0) {
              Logger.error(`stderr Mikrotik TRACEROUTE: ${chunk.length} bytes recebidos`);
            }
          }
        };

        const result = await this.ssh.execCommand(cmd, sshOptions);

        if (!completed) {
          completed = true;
          clearInterval(progressMonitor);
          clearTimeout(absoluteTimeout);

          const cleanStdout = this.cleanAnsiOutput(result.stdout);
          const cleanStderr = this.cleanAnsiOutput(result.stderr);

          this.analyzeDiagnosticOutput(cleanStdout, false, true);

          resolve({
            stdout: this.formatMikrotikOutput(cleanStdout),
            stderr: cleanStderr,
            code: result.code || 0
          });
        }
      } catch (error) {
        if (!completed) {
          completed = true;
          clearInterval(progressMonitor);
          clearTimeout(absoluteTimeout);

          const errorMessage = error instanceof Error ? error.message : String(error);
          Logger.error(`Erro no comando TRACEROUTE: ${errorMessage}`);
          reject(error);
        }
      }
    });
  }

  /**
   * Conta o número de hops na saída do traceroute
   */
  private countHopsInOutput(output: string): number {
    if (!output) return 0;

    const lines = output.split('\n');
    const hopLines = lines.filter(line => /^\s*\d+\s+/.test(line));

    return hopLines.length;
  }

  /**
   * Analisa a saída de comandos de diagnóstico para verificar se estão completos
   * @param cleanStdout Saída limpa
   * @param isPing Se é comando ping
   * @param isTrace Se é comando traceroute
   */
  private analyzeDiagnosticOutput(cleanStdout: string, isPing: boolean, isTrace: boolean): void {
    const done = cleanStdout.toLowerCase();

    Logger.log(`Analisando saída do comando ${isPing ? 'PING' : 'TRACEROUTE'} para detecção de fim...`);
    Logger.log(`Saída completa (${cleanStdout.length} chars): "${cleanStdout.substring(0, 200)}${cleanStdout.length > 200 ? '...' : ''}"`);

    // Padrões mais abrangentes para detecção de fim
    const pingComplete = isPing && (
      (done.includes('sent=') && done.includes('received=')) ||
      done.includes('packets transmitted') ||
      done.includes('packet loss') ||
      done.includes('ping statistics') ||
      done.includes('interrupted')
    );

    const traceComplete = isTrace && this.isTracerouteComplete(cleanStdout);

    if (pingComplete || traceComplete) {
      Logger.log(`Comando ${isPing ? 'PING' : 'TRACEROUTE'} completado com sucesso - padrão de fim detectado`);
    } else {
      Logger.log(`Comando ${isPing ? 'PING' : 'TRACEROUTE'} pode estar incompleto - padrões de fim não detectados`);
      Logger.log(`Últimas 3 linhas da saída: "${cleanStdout.split('\n').slice(-3).join('\\n')}"`);
    }
  }

  /**
   * Verifica se o traceroute completou baseado na análise da saída
   * @param output Saída do comando traceroute
   * @returns true se o traceroute completou
   */
  private isTracerouteComplete(output: string): boolean {
    const lines = output.split('\n').filter(line => line.trim().length > 0);

    // Padrões de conclusão explícita - mais rigorosos
    const done = output.toLowerCase();
    if (done.includes('trace complete') ||
        done.includes('destination reached') ||
        done.includes('no route to host') ||
        done.includes('network unreachable') ||
        done.includes('traceroute complete')) {
      Logger.log('Traceroute completado - padrão de conclusão explícita detectado');
      return true;
    }

    // Analisar estrutura da tabela para detectar conclusão
    const tableLines = lines.filter(line => {
      // Filtrar linhas que parecem ser dados de hops (contêm números e IPs)
      return /^\s*\d+\s+/.test(line) &&
             (line.includes('.') || line.includes('*') || line.includes('timeout'));
    });

    if (tableLines.length === 0) {
      Logger.log('Traceroute - nenhuma linha de dados encontrada');
      return false;
    }

    Logger.log(`Traceroute - analisando ${tableLines.length} linhas de dados`);

    // Extrair números dos hops
    const hopNumbers = tableLines.map(line => {
      const match = line.match(/^\s*(\d+)\s+/);
      return match ? parseInt(match[1]) : 0;
    }).filter(num => num > 0);

    if (hopNumbers.length === 0) {
      Logger.log('Traceroute - nenhum número de hop válido encontrado');
      return false;
    }

    const maxHop = Math.max(...hopNumbers);
    const lastLines = tableLines.slice(-3); // Últimas 3 linhas

    Logger.log(`Traceroute - hop máximo detectado: ${maxHop}`);
    Logger.log(`Traceroute - últimas linhas: ${lastLines.map(l => l.trim()).join(' | ')}`);

    // Verificar se atingiu o destino (hop com resposta válida) - mais rigoroso
    const hasDestinationResponse = lastLines.some(line => {
      return line.includes('ms') && !line.includes('100%') && !line.includes('timeout') && !line.includes('*');
    });

    if (hasDestinationResponse && maxHop >= 3) {
      Logger.log('Traceroute completado - resposta do destino detectada com dados suficientes');
      return true;
    }

    // Verificar se atingiu limite de hops (30 é o padrão que definimos) - mais rigoroso
    if (maxHop >= 25) {
      Logger.log('Traceroute completado - limite próximo ao máximo de hops atingido (25+)');
      return true;
    }

    // Verificar se há múltiplos hops consecutivos com timeout (indica fim) - mais rigoroso
    const consecutiveTimeouts = this.countConsecutiveTimeouts(tableLines);
    if (consecutiveTimeouts >= 5 && maxHop >= 3) {
      Logger.log(`Traceroute completado - ${consecutiveTimeouts} hops consecutivos com timeout e dados suficientes`);
      return true;
    }

    // Verificar se não há progressão (mesmo hop repetido várias vezes) - mais rigoroso
    const recentHops = hopNumbers.slice(-8); // Últimos 8 hops
    const uniqueRecentHops = [...new Set(recentHops)];
    if (recentHops.length >= 8 && uniqueRecentHops.length <= 2 && maxHop >= 5) {
      Logger.log('Traceroute completado - sem progressão detectada nos últimos hops com dados suficientes');
      return true;
    }

    Logger.log('Traceroute ainda em progresso - critérios de conclusão não atendidos');
    return false;
  }

  /**
   * Conta hops consecutivos com timeout no final da saída
   * @param tableLines Linhas da tabela de traceroute
   * @returns Número de hops consecutivos com timeout
   */
  private countConsecutiveTimeouts(tableLines: string[]): number {
    let count = 0;

    // Analisar de trás para frente
    for (let i = tableLines.length - 1; i >= 0; i--) {
      const line = tableLines[i].toLowerCase();
      if (line.includes('100%') || line.includes('timeout') || line.includes('* * *')) {
        count++;
      } else if (line.includes('ms') && !line.includes('100%')) {
        // Se encontrar uma resposta válida, parar a contagem
        break;
      }
    }

    return count;
  }

  /**
   * Executa um comando em um dispositivo Mikrotik
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Atualizar timestamp do último comando
      this.lastCommandTime = Date.now();

      Logger.log('Executando comando Mikrotik:', command);

      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando multilinhas para Mikrotik');
        return await this.executeMultilineCommand(command);
      }

      // Verificar se é um comando interativo
      const isInteractive = this.isInteractiveCommand(command);
      Logger.log(`Comando ${isInteractive ? 'interativo' : 'não-interativo'} detectado: ${command}`);

      if (isInteractive) {
        return await this.executeInteractiveCommand(command);
      }

      // Formatar o comando usando a função específica para diagnósticos
      const formattedCommand = this.formatDiagnosticCommand(command);

      Logger.log(`Executando comando Mikrotik formatado: ${formattedCommand}`);

      // Usar apenas SSH (API desabilitada)
      Logger.log('Usando SSH para executar comando Mikrotik');

      // Desativar paginação antes de executar o comando
      try {
        await this.ssh.execCommand('terminal length=0', {
          execOptions: { pty: false }
        });
        Logger.log('Paginação desativada com sucesso');
      } catch (error) {
        Logger.log('Aviso: Não foi possível desativar paginação:', error);
        // Continuar mesmo se não conseguir desativar paginação
      }

      // Calcular o comando sem a barra inicial para SSH
      const cmd = formattedCommand.startsWith('/') ? formattedCommand.slice(1) : formattedCommand;

      // Detectar comandos de diagnóstico
      const isPing = command.toLowerCase().includes('ping');
      const isTrace = command.toLowerCase().includes('traceroute');

      // Para comandos de diagnóstico, usar timeout interno menor que o do controller
      if (isPing || isTrace) {
        return await this.executeDiagnosticCommand(cmd, isPing, isTrace);
      }

      // Configurar opções SSH otimizadas para comandos normais
      const sshOptions = {
        execOptions: {
          pty: false,  // Desabilitar PTY para melhor estabilidade
          env: {}      // Ambiente limpo
        },
        timeout: this.calculateDynamicTimeout(1),
        onStdout: (chunk: any) => {
          if (chunk.length > 0) {
            Logger.log(`stdout Mikrotik: ${chunk.length} bytes recebidos`);
          }
        },
        onStderr: (chunk: any) => {
          if (chunk.length > 0) {
            Logger.error(`stderr Mikrotik: ${chunk.length} bytes recebidos`);
          }
        }
      };

      // Executar o comando via SSH com retry automático
      let result;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          Logger.log(`Tentativa ${retryCount + 1}/${maxRetries + 1} de execução do comando`);

          result = await this.ssh.execCommand(cmd, sshOptions);

          // Se chegou aqui, comando executado com sucesso
          break;
        } catch (error) {
          retryCount++;
          const errorMessage = error instanceof Error ? error.message : String(error);

          Logger.error(`Erro na tentativa ${retryCount}/${maxRetries + 1}:`, errorMessage);

          // Verificar se é um erro de timeout/keepalive
          if (errorMessage.includes('keepalive') ||
              errorMessage.includes('timeout') ||
              errorMessage.includes('timed out') ||
              errorMessage.includes('connection') ||
              errorMessage.includes('closed')) {

            if (retryCount <= maxRetries) {
              Logger.log(`Aguardando 2 segundos antes da próxima tentativa...`);
              await new Promise(resolve => setTimeout(resolve, 2000));
              continue;
            } else {
              Logger.error('Máximo de tentativas excedido, sinalizando necessidade de reconexão');
              throw new Error('RECONNECT_NEEDED: Falha persistente de conexão SSH');
            }
          } else {
            // Erro não relacionado a conexão, não tentar novamente
            throw error;
          }
        }
      }

      if (!result) {
        throw new Error('Falha ao executar comando após todas as tentativas');
      }

      // Limpar a saída final
      const cleanStdout = this.cleanAnsiOutput(result.stdout);
      const cleanStderr = this.cleanAnsiOutput(result.stderr);

      // Formatar a saída para melhor legibilidade
      const formattedOutput = this.formatMikrotikOutput(cleanStdout);

      Logger.log(`Comando Mikrotik executado com sucesso. Saída: ${formattedOutput.length} caracteres`);

      return {
        stdout: formattedOutput,
        stderr: cleanStderr,
        code: result.code || 0
      };
    } catch (error) {
      Logger.error('Erro ao executar comando Mikrotik:', error);

      // Verificar se é um erro de keepalive timeout
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('RECONNECT_NEEDED') ||
          errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('timed out') ||
          errorMessage.includes('connection') ||
          errorMessage.includes('closed')) {
        Logger.log('Detectado erro de conexão, sinalizando necessidade de reconexão');
        throw new Error('RECONNECT_NEEDED: Erro de conexão SSH detectado');
      }

      throw error;
    }
  }

  /**
   * Executa um comando com múltiplas linhas
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  async executeMultilineCommand(command: string): Promise<CommandResult> {
    try {
      // Atualizar timestamp do último comando
      this.lastCommandTime = Date.now();

      // Dividir o comando em linhas individuais
      const lines = command.split('\n').filter(line => line.trim() !== '');
      const commandCount = lines.length;

      // Calcular o timeout dinâmico com base na quantidade de comandos
      const dynamicTimeout = this.calculateDynamicTimeout(commandCount);

      Logger.log(`Executando ${commandCount} comandos Mikrotik separados com timeout dinâmico de ${dynamicTimeout}ms`);

      // Usar apenas SSH (API desabilitada)
      Logger.log('Usando SSH para executar comandos múltiplos Mikrotik');

      // Criar uma promise com timeout global para todos os comandos
      const timeoutPromise = new Promise<CommandResult>((resolve) => {
        setTimeout(() => {
          resolve({
            stdout: '',
            stderr: `[ERRO] Timeout global atingido após ${dynamicTimeout/1000} segundos para ${commandCount} comandos`,
            code: 124 // Código de timeout
          });
        }, dynamicTimeout);
      });

      // Promise para executar todos os comandos
      const executePromise = async (): Promise<CommandResult> => {
        let combinedOutput = '';
        let combinedError = '';
        let lastCode = 0;
        let successfulCommands = 0;

        // Executar cada linha separadamente
        for (let i = 0; i < commandCount; i++) {
          const line = lines[i].trim();
          Logger.log(`Executando linha ${i+1}/${commandCount}: ${line}`);

          // Formatar o comando para SSH
          const cleanCommand = line.replace(/\s+/g, ' ');
          const formattedCommand = cleanCommand.startsWith('/') ? cleanCommand : `/${cleanCommand}`;

          try {
            // Retira a barra inicial
            const cmd = formattedCommand.startsWith('/')
              ? formattedCommand.slice(1)
              : formattedCommand;

            // Configurar opções SSH otimizadas
            const sshOptions = {
              execOptions: {
                pty: false,  // Desabilitar PTY para melhor estabilidade
                env: {}      // Ambiente limpo
              },
              timeout: Math.min(this.calculateDynamicTimeout(1), 30000), // Timeout por comando individual
              onStdout: (chunk: any) => {
                if (chunk.length > 0) {
                  Logger.log(`stdout linha ${i+1}/${commandCount}: ${chunk.length} bytes`);
                }
              },
              onStderr: (chunk: any) => {
                if (chunk.length > 0) {
                  Logger.error(`stderr linha ${i+1}/${commandCount}: ${chunk.length} bytes`);
                }
              }
            };

            // Executar comando com retry limitado
            let result;
            let retryCount = 0;
            const maxRetries = 1; // Apenas 1 retry para comandos múltiplos

            while (retryCount <= maxRetries) {
              try {
                result = await this.ssh.execCommand(cmd, sshOptions);
                break; // Sucesso, sair do loop de retry
              } catch (error) {
                retryCount++;
                const errorMessage = error instanceof Error ? error.message : String(error);

                if (retryCount <= maxRetries &&
                    (errorMessage.includes('timeout') || errorMessage.includes('connection'))) {
                  Logger.log(`Retry ${retryCount}/${maxRetries} para linha ${i+1}`);
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  continue;
                } else {
                  throw error;
                }
              }
            }

            if (result) {
              // Limpar e formatar a saída
              const cleanStdout = this.cleanAnsiOutput(result.stdout);
              const formattedStdout = this.formatMikrotikOutput(cleanStdout);
              combinedOutput += `=== Comando: ${formattedCommand} ===\n${formattedStdout}\n\n`;

              if (result.stderr) {
                const cleanStderr = this.cleanAnsiOutput(result.stderr);
                combinedError += `=== Aviso em: ${formattedCommand} ===\n${cleanStderr}\n\n`;
              }

              lastCode = result.code || 0;
              successfulCommands++;
            }
          } catch (error) {
            Logger.error(`Erro ao executar linha ${i+1}/${commandCount}:`, error);
            const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
            combinedError += `=== Erro em: ${formattedCommand} ===\n${errorMessage}\n\n`;
            lastCode = 1;

            // Verificar se é erro crítico de conexão
            if (errorMessage.includes('keepalive') ||
                errorMessage.includes('connection') ||
                errorMessage.includes('closed')) {
              Logger.error('Erro crítico de conexão detectado, interrompendo execução múltipla');
              combinedError += `\n[ERRO CRÍTICO] Conexão SSH perdida. Comandos restantes cancelados.\n`;
              break;
            }
          }

          // Pequena pausa entre comandos para evitar sobrecarga
          if (i < commandCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        Logger.log(`Execução múltipla concluída: ${successfulCommands}/${commandCount} comandos executados com sucesso`);

        return {
          stdout: combinedOutput,
          stderr: combinedError,
          code: lastCode
        };
      };

      // Executar com timeout
      return Promise.race([executePromise(), timeoutPromise]);
    } catch (error) {
      Logger.error('Erro ao executar comandos múltiplos Mikrotik:', error);

      // Verificar se é erro de conexão
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('connection') ||
          errorMessage.includes('closed')) {
        throw new Error('RECONNECT_NEEDED: Erro de conexão durante execução múltipla');
      }

      throw error;
    }
  }

  /**
   * Limpa caracteres de controle ANSI da saída do terminal
   * @param output Saída do terminal
   * @returns Saída limpa
   */
  private cleanAnsiOutput(output: string): string {
    try {
      if (!output) return '';

      // Remover sequências ANSI mais abrangentes para melhor limpeza
      let cleaned = output;

      // Remover sequências ANSI padrão
      cleaned = cleaned.replace(/\x1B\[[0-?]*[ -/]*[@-~]/g, '');

      // Remover sequências de escape adicionais
      cleaned = cleaned.replace(/\x1B\[[0-9;]*[mGKH]/g, '');
      cleaned = cleaned.replace(/\x1B\[[\d;]*[A-Za-z]/g, '');

      // Remover caracteres de controle
      cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

      // Normalizar quebras de linha
      cleaned = cleaned.replace(/\r\n?/g, '\n');

      // Remover linhas vazias excessivas no início e fim
      cleaned = cleaned.replace(/^\n+/, '').replace(/\n+$/, '');

      return cleaned;
    } catch (error) {
      Logger.error('Erro ao limpar saída ANSI:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata a saída do Mikrotik para melhor legibilidade
   * @param output Saída limpa do Mikrotik
   * @returns Saída formatada
   */
  private formatMikrotikOutput(output: string): string {
    try {
      if (!output) return '';

      // Dividir em linhas para processamento
      const lines = output.split('\n');

      // Detectar se é uma tabela de traceroute
      const isTracerouteTable = lines.some(line =>
        line.includes('ADDRESS') &&
        (line.includes('LOSS') || line.includes('SENT') || line.includes('LAST'))
      );

      // Detectar se é uma tabela simples
      const isTable = lines.some(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU') || line.includes('INTERFACE'))
      );

      if (isTracerouteTable) {
        // Formatar especificamente para traceroute
        return this.formatTracerouteTable(lines);
      } else if (isTable) {
        // Formatar como tabela simples
        return this.formatMikrotikTable(lines);
      }

      // Formatar saída padrão - apenas limpar e organizar
      const formattedLines = lines
        .filter(line => line.trim().length > 0); // Remover linhas vazias

      return formattedLines.join('\n');
    } catch (error) {
      Logger.error('Erro ao formatar saída Mikrotik:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata especificamente uma tabela de traceroute do Mikrotik
   * @param lines Linhas da tabela de traceroute
   * @returns Tabela de traceroute formatada
   */
  private formatTracerouteTable(lines: string[]): string {
    try {
      // Encontrar a linha de cabeçalho do traceroute
      const headerIndex = lines.findIndex(line =>
        line.includes('ADDRESS') &&
        (line.includes('LOSS') || line.includes('SENT') || line.includes('LAST'))
      );

      if (headerIndex === -1) {
        // Não é uma tabela de traceroute, retornar as linhas filtradas
        return lines.filter(line => line.trim().length > 0).join('\n');
      }

      let formattedOutput = '';

      // Adicionar todas as linhas a partir do cabeçalho, preservando formatação
      for (let i = headerIndex; i < lines.length; i++) {
        const line = lines[i];

        // Pular linhas completamente vazias
        if (!line.trim()) continue;

        // Preservar a formatação original da linha, apenas removendo espaços excessivos no início/fim
        const cleanLine = line.replace(/^\s+/, '').replace(/\s+$/, '');

        if (cleanLine) {
          formattedOutput += cleanLine + '\n';
        }
      }

      return formattedOutput.trim();
    } catch (error) {
      Logger.error('Erro ao formatar tabela de traceroute Mikrotik:', error);
      return lines.filter(line => line.trim().length > 0).join('\n');
    }
  }

  /**
   * Formata uma tabela do Mikrotik de forma simples
   * @param lines Linhas da tabela
   * @returns Tabela formatada
   */
  private formatMikrotikTable(lines: string[]): string {
    try {
      // Encontrar a linha de cabeçalho
      const headerIndex = lines.findIndex(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU') || line.includes('INTERFACE'))
      );

      if (headerIndex === -1) {
        // Não é uma tabela, retornar as linhas originais
        return lines.filter(line => line.trim().length > 0).join('\n');
      }

      // Formatar de forma simples - apenas organizar as linhas
      let formattedOutput = '';

      // Adicionar todas as linhas a partir do cabeçalho
      for (let i = headerIndex; i < lines.length; i++) {
        const line = lines[i].trim();

        // Pular linhas vazias
        if (!line) continue;

        // Adicionar linha com formatação mínima
        formattedOutput += line + '\n';
      }

      return formattedOutput;
    } catch (error) {
      Logger.error('Erro ao formatar tabela Mikrotik:', error);
      return lines.filter(line => line.trim().length > 0).join('\n');
    }
  }

  /**
   * Executa um comando interativo em um dispositivo Mikrotik usando shell
   * @param command Comando interativo a ser executado
   * @returns Resultado do comando
   */
  async executeInteractiveCommand(command: string): Promise<CommandResult> {
    return new Promise(async (resolve, reject) => {
      let shell: any = null;
      let output = '';
      let errorOutput = '';
      let commandCompleted = false;
      let timeoutId: NodeJS.Timeout | null = null;

      try {
        // Atualizar timestamp do último comando
        this.lastCommandTime = Date.now();

        Logger.log(`Executando comando interativo Mikrotik: ${command}`);

        // Formatar o comando usando a função específica para diagnósticos
        const formattedCommand = this.formatDiagnosticCommand(command);

        // Timeout padrão para comandos interativos
        let interactiveTimeout = 90000; // 90 segundos padrão

        // Configurar timeout global
        timeoutId = setTimeout(() => {
          if (!commandCompleted) {
            Logger.log('Timeout atingido para comando interativo Mikrotik');
            commandCompleted = true;
            if (shell) {
              try {
                shell.end();
              } catch (e) {
                // Ignorar erros ao fechar shell
              }
            }
            resolve({
              stdout: output || '[TIMEOUT] Comando interativo não completou dentro do tempo limite',
              stderr: errorOutput,
              code: 124 // Código de timeout
            });
          }
        }, interactiveTimeout);

        // Iniciar shell interativo com PTY habilitado
        shell = await this.ssh.requestShell({
          term: 'vt100',
          rows: 24,
          cols: 120,
          wrap: 120,
          ptyType: 'vanilla'
        });

        // Variáveis de controle
        let commandSent = false;
        let mikrotikReady = false;

        // Configurar handlers para eventos
        shell.on('data', (data: Buffer) => {
          if (commandCompleted) return;

          const chunk = data.toString('utf8');
          output += chunk;

          Logger.log(`Recebido comando interativo (${chunk.length} bytes): ${chunk.substring(0, 200)}${chunk.length > 200 ? '...' : ''}`);

          // Detectar quando o Mikrotik está pronto para receber comandos
          if (!mikrotikReady && (
              chunk.includes('[') && chunk.includes('@') && chunk.includes('] >') ||
              chunk.includes('RouterOS') ||
              chunk.includes('MikroTik'))) {
            Logger.log('Mikrotik pronto detectado, aguardando antes de enviar comando...');
            mikrotikReady = true;

            // Aguardar um pouco e então enviar o comando
            setTimeout(() => {
              if (!commandCompleted && !commandSent) {
                Logger.log(`Enviando comando para Mikrotik: ${formattedCommand}`);
                shell.write(formattedCommand + '\r\n');
                commandSent = true;
              }
            }, 2000); // Aguardar 2 segundos após detectar que está pronto
            return;
          }

          // Verificar se detectamos o prompt "More:" para continuar
          if (chunk.includes('More:') || chunk.includes('[Q quit|SPACE continue|ENTER line]')) {
            shell.write(' ');
            Logger.log('Detectado prompt de paginação, enviando espaço para continuar');
            return;
          }

          // Detectar prompt do Mikrotik para comandos interativos
          // Padrões: [admin@MikroTik] >, [william@CALL CENTER] >, [user@hostname] >
          if ((chunk.includes('[') && chunk.includes('@') && chunk.includes('] >')) ||
              chunk.includes('RouterOS') && chunk.includes('> ') ||
              chunk.includes('MikroTik') && chunk.includes('> ')) {
            Logger.log('Prompt do Mikrotik detectado, comando interativo completado');
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            shell.end();
            resolve({
              stdout: this.cleanAnsiOutput(output),
              stderr: this.cleanAnsiOutput(errorOutput),
              code: 0
            });
            return;
          }
        });

        shell.stderr?.on('data', (data: Buffer) => {
          if (commandCompleted) return;
          const chunk = data.toString('utf8');
          errorOutput += chunk;
          Logger.error(`stderr comando interativo: ${chunk}`);
        });

        shell.on('close', () => {
          if (!commandCompleted) {
            Logger.log('Shell fechado para comando interativo');
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            resolve({
              stdout: this.cleanAnsiOutput(output),
              stderr: this.cleanAnsiOutput(errorOutput),
              code: 0
            });
          }
        });

        shell.on('error', (error: Error) => {
          if (!commandCompleted) {
            Logger.error('Erro no shell interativo:', error);
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            reject(error);
          }
        });

        // Aguardar um pouco para o shell estar pronto
        await new Promise(resolve => setTimeout(resolve, 1000));

        // O comando será enviado automaticamente quando o Mikrotik estiver pronto
        Logger.log(`Aguardando Mikrotik ficar pronto para enviar comando: ${formattedCommand}`);

        // Timeout de segurança caso o Mikrotik não fique pronto
        setTimeout(() => {
          if (!commandSent && !commandCompleted) {
            Logger.log('Timeout de segurança: enviando comando mesmo sem detectar prompt');
            shell.write(formattedCommand + '\r\n');
            commandSent = true;
          }
        }, 10000); // 10 segundos de timeout de segurança

      } catch (error) {
        Logger.error('Erro ao executar comando interativo Mikrotik:', error);
        commandCompleted = true;
        if (timeoutId) clearTimeout(timeoutId);
        if (shell) {
          try {
            shell.end();
          } catch (e) {
            // Ignorar erros ao fechar shell
          }
        }
        reject(error);
      }
    });
  }
}
