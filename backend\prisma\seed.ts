import { PrismaClient, Role, OS } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Criar usuário admin
  const adminPassword = await bcrypt.hash('123456', 8)
  const userPassword = await bcrypt.hash('123456', 8)
  
  // Criar usuário admin
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administrador',
      password: adminPassword,
      role: Role.ADMIN,
      active: true,
    },
  })

  // Criar usuário comum
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      password: userPassword,
      role: Role.USER,
      active: true,
    },
  })

  // Criar servidor exemplo
  const server = await prisma.server.create({
    data: {
      name: '<PERSON><PERSON><PERSON> de Desenvolvimento',
      host: 'localhost',
      port: 22,
      username: 'dev',
      password: 'senha123',
      os: OS.LINUX,
      userId: admin.id,
    },
  })

  // Criar comando exemplo
  const command = await prisma.command.create({
    data: {
      name: 'Verificar Status',
      command: 'systemctl status',
      description: 'Verifica o status dos serviços do sistema',
      serverId: server.id,
    },
  })

  // Criar template de comando
  const commandTemplate = await prisma.commandTemplate.create({
    data: {
      name: 'Template de Manutenção',
      description: 'Comandos básicos para manutenção do servidor',
      isPublic: true,
      userId: admin.id,
      commands: {
        create: [
          {
            name: 'Verificar Memória',
            command: 'free -h',
            description: 'Mostra o uso de memória',
            order: 1,
          },
          {
            name: 'Verificar Disco',
            command: 'df -h',
            description: 'Mostra o uso de disco',
            order: 2,
          },
        ],
      },
    },
  })

  // Criar acesso ao servidor para o usuário comum
  await prisma.serverUser.create({
    data: {
      userId: user.id,
      serverId: server.id,
    },
  })

  // Criar histórico de comando
  await prisma.commandHistory.create({
    data: {
      userId: admin.id,
      serverId: server.id,
      commandId: command.id,
      result: 'Todos os serviços estão funcionando normalmente',
      status: 0,
    },
  })

  console.log('Seed concluído com sucesso!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 