#!/bin/bash

# Script para restaurar o backup mais recente do banco de dados

# Encontrar o backup mais recente
LATEST_BACKUP=$(find /backups -name "backup_*.sql" -type f -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2-)

if [ -z "$LATEST_BACKUP" ]; then
  echo "Nenhum backup encontrado!"
  exit 1
fi

echo "Restaurando backup: $LATEST_BACKUP"

# Restaurar o backup
PGPASSWORD=postgres psql -h postgres -U postgres -d sem_fronteiras -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
PGPASSWORD=postgres psql -h postgres -U postgres -d sem_fronteiras < "$LATEST_BACKUP"

echo "Backup restaurado com sucesso!"
