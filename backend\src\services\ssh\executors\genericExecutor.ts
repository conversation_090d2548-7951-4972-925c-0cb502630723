import { NodeSSH } from 'node-ssh';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos genéricos
 */
export class GenericExecutor extends BaseExecutor {
  constructor(ssh: NodeSSH) {
    super(ssh);
  }

  /**
   * Executa um comando em um dispositivo genérico
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando com múltiplas linhas, executando linha por linha');
        return await this.executeMultilineCommand(command);
      }

      // Limpar o comando para remover caracteres invisíveis e espaços extras
      const cleanCommand = command.trim().replace(/\s+/g, ' ');

      Logger.log(`Executando comando genérico: ${cleanCommand.substring(0, 50)}${cleanCommand.length > 50 ? '...' : ''}`);

      // Lista de comandos que precisam de shell interativo para outros dispositivos
      const interactiveCommands = ['show', 'configure', 'terminal', 'enable', 'disable', 'router', 'static-routes', 'community', 'delete', 'commit', 'info'];
      const needsInteractive = interactiveCommands.some(cmd => command.startsWith(cmd)) ||
                              command.includes('|') ||
                              command.includes('>') ||
                              command.includes('<');

      // Para comandos que precisam de shell interativo
      if (needsInteractive) {
        Logger.log('Usando shell interativo para o comando');
        return await this.executeInteractiveCommand(cleanCommand);
      }

      // Para comandos simples
      Logger.log('Usando execCommand para comando simples');
      const result = await this.ssh.execCommand(cleanCommand, {
        cwd: '/',
        onStdout: (chunk) => {
          Logger.log(`stdout (${chunk.length} bytes): ${chunk.toString('utf8').substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
        },
        onStderr: (chunk) => {
          Logger.error(`stderr (${chunk.length} bytes): ${chunk.toString('utf8').substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
        }
      });

      return {
        stdout: result.stdout,
        stderr: result.stderr,
        code: result.code || 0,
      };
    } catch (error) {
      Logger.error('Erro ao executar comando genérico:', error);
      throw new Error(`Falha ao executar comando genérico: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Executa um comando interativo em um dispositivo genérico
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  protected executeInteractiveCommand(command: string): Promise<CommandResult> {
    return super.executeInteractiveCommand(command, {
      term: 'vt100',
      rows: 24,
      cols: 80,
      maxExecutionTime: 30000, // 30 segundos padrão
      inactivityTimeout: 3000 // 3 segundos padrão
    });
  }
}
