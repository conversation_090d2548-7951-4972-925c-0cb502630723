import React, { useRef, useState } from 'react'
import { Upload, FileText, Check, AlertCircle } from 'lucide-react'

interface ScriptFileUploadProps {
  onFileContent: (content: string) => void
  className?: string
}

export function ScriptFileUpload({ onFileContent, className = '' }: ScriptFileUploadProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validar extensão do arquivo
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (extension !== 'txt' && extension !== 'sh') {
      setUploadStatus('error')
      setFileName(`${file.name} (formato não suportado)`)
      if (inputRef.current) {
        inputRef.current.value = ''
      }
      return
    }

    setFileName(file.name)
    setUploadStatus('idle')

    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        // Garante que o conteúdo seja tratado como texto
        const normalizedContent = content.replace(/\r\n/g, '\n')
        onFileContent(normalizedContent)
        
        // Mostrar sucesso
        setUploadStatus('success')
        
        // Limpar input após sucesso (mas manter o nome do arquivo)
        if (inputRef.current) {
          inputRef.current.value = ''
        }
      } catch (error) {
        console.error('Erro ao processar arquivo:', error)
        setUploadStatus('error')
      }
    }

    reader.onerror = () => {
      console.error('Erro ao ler arquivo')
      setUploadStatus('error')
    }

    reader.readAsText(file)
  }

  const handleClick = () => {
    inputRef.current?.click()
  }

  return (
    <div className={`${className} flex items-center gap-2`}>
      <input
        ref={inputRef}
        type="file"
        accept=".txt,.sh"
        onChange={handleFileChange}
        className="hidden"
      />
      <button
        type="button"
        onClick={handleClick}
        className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
        title="Carregar arquivo .sh ou .txt"
      >
        <Upload size={16} />
        Carregar script
      </button>
      
      {fileName && (
        <div className="flex items-center gap-2 text-sm">
          <FileText size={16} className="text-gray-500" />
          <span className="truncate max-w-[200px]">{fileName}</span>
          {uploadStatus === 'success' && (
            <Check size={16} className="text-green-500" />
          )}
          {uploadStatus === 'error' && (
            <AlertCircle size={16} className="text-red-500" />
          )}
        </div>
      )}
    </div>
  )
}