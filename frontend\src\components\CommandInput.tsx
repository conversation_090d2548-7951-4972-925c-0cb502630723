import React, { useEffect, useRef, useState } from 'react'
import { Terminal } from 'lucide-react'

interface CommandInputProps {
  name: string
  register: any // Tipo do register do react-hook-form
  error?: string
  label?: string
  placeholder?: string
  className?: string
  defaultValue?: string
}

export function CommandInput({
  name,
  register,
  error,
  label,
  placeholder,
  className = '',
  defaultValue = '',
}: CommandInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement | null>(null)
  const [value, setValue] = useState('')
  const registration = register(name)

  // Sincroniza o valor do textarea com o estado local e inicializa com o defaultValue
  useEffect(() => {
    if (textareaRef.current) {
      // Inicializar com o defaultValue se fornecido
      if (defaultValue && textareaRef.current.value === '') {
        textareaRef.current.value = defaultValue;
        setValue(defaultValue);

        // Atualizar o valor no formulário
        registration.onChange({
          target: {
            name,
            value: defaultValue
          }
        });
      } else {
        const currentValue = textareaRef.current.value;
        if (currentValue !== value) {
          setValue(currentValue);
        }
      }
    }
  }, [defaultValue])

  // Manipula mudanças no textarea
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    registration.onChange(e)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab') {
      e.preventDefault()
      const target = e.currentTarget
      const start = target.selectionStart
      const end = target.selectionEnd
      const currentValue = target.value
      const newValue = currentValue.substring(0, start) + '  ' + currentValue.substring(end)

      // Atualiza o estado local
      setValue(newValue)

      // Atualiza o valor no formulário
      registration.onChange({
        target: {
          name,
          value: newValue
        }
      })

      // Atualiza a posição do cursor
      setTimeout(() => {
        target.selectionStart = target.selectionEnd = start + 2
      }, 0)
    }
  }

  // Auto-resize textarea baseado no conteúdo
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${textarea.scrollHeight}px`
    }
  }, [value])

  // Verifica se o valor foi alterado externamente (como no upload de arquivo)
  useEffect(() => {
    const checkExternalChanges = () => {
      if (textareaRef.current) {
        const fieldValue = textareaRef.current.value
        if (fieldValue !== value) {
          console.log(`Valor do campo ${name} alterado externamente: ${fieldValue}`);
          setValue(fieldValue)

          // Garantir que o valor seja atualizado no formulário
          registration.onChange({
            target: {
              name,
              value: fieldValue
            }
          });
        }
      }
    }

    // Verifica a cada 200ms se o valor foi alterado externamente
    // Aumentamos o intervalo para reduzir o impacto no desempenho
    const interval = setInterval(checkExternalChanges, 200)
    return () => clearInterval(interval)
  }, [value, name])

  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Terminal className="h-5 w-5 text-gray-400" />
        </div>
        <textarea
          {...registration}
          ref={(e) => {
            // Registra o elemento no react-hook-form
            registration.ref(e)
            // Atualiza a referência local
            textareaRef.current = e
          }}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`
            block w-full pl-10 pr-3 py-2 min-h-[80px] max-h-[80px] overflow-y-auto resize-none
            font-mono text-sm
            border border-gray-300 rounded-md
            shadow-sm placeholder-gray-400
            focus:outline-none focus:ring-blue-500 focus:border-blue-500
            ${error ? 'border-red-500' : ''}
          `}
        />
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}