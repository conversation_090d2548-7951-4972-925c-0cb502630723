/**
 * Script de diagnóstico para problemas com servidores HarmonyOS
 * 
 * Este script pode ser executado quando houver problemas com servidores HarmonyOS
 * Ele verifica a conexão, executa comandos de diagnóstico e gera um relatório
 */

const { NodeSSH } = require('node-ssh');
const fs = require('fs');
const path = require('path');

// Configurações
const REPORT_DIR = path.join(__dirname, '../logs/harmony-diagnostics');
const MAX_EXECUTION_TIME = 30000; // 30 segundos

// Criar diretório de logs se não existir
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true });
}

// Função para registrar logs
function log(message, server = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  
  if (server) {
    const logFile = path.join(REPORT_DIR, `${server.host.replace(/\./g, '_')}.log`);
    fs.appendFileSync(logFile, logMessage);
  }
}

// Função para executar comando com timeout
async function executeCommandWithTimeout(ssh, command, timeout = MAX_EXECUTION_TIME) {
  return new Promise((resolve) => {
    const timer = setTimeout(() => {
      resolve({
        stdout: '',
        stderr: 'Timeout: O comando excedeu o tempo limite de execução',
        code: 124
      });
    }, timeout);
    
    ssh.execCommand(command, {
      cwd: '/',
      onStdout: (chunk) => {
        console.log(`stdout: ${chunk.toString('utf8')}`);
      },
      onStderr: (chunk) => {
        console.error(`stderr: ${chunk.toString('utf8')}`);
      }
    }).then((result) => {
      clearTimeout(timer);
      resolve(result);
    }).catch((error) => {
      clearTimeout(timer);
      resolve({
        stdout: '',
        stderr: `Erro: ${error.message}`,
        code: 1
      });
    });
  });
}

// Função para diagnosticar um servidor HarmonyOS
async function diagnoseHarmonyServer(server) {
  const ssh = new NodeSSH();
  const reportFile = path.join(REPORT_DIR, `${server.host.replace(/\./g, '_')}_report.json`);
  
  log(`Iniciando diagnóstico para servidor ${server.name} (${server.host})`, server);
  
  const report = {
    server: {
      name: server.name,
      host: server.host,
      port: server.port
    },
    timestamp: new Date().toISOString(),
    connection: {
      success: false,
      error: null
    },
    commands: [],
    recommendations: []
  };
  
  try {
    // Configuração SSH específica para HarmonyOS
    const config = {
      host: server.host,
      port: server.port,
      username: server.username,
      readyTimeout: 30000,
      keepaliveInterval: 10000,
      algorithms: {
        kex: [
          'diffie-hellman-group-exchange-sha1',
          'diffie-hellman-group14-sha1',
          'diffie-hellman-group1-sha1'
        ],
        cipher: [
          'aes128-ctr',
          'aes192-ctr',
          'aes256-ctr',
          'aes128-gcm',
          '<EMAIL>',
          'aes256-gcm',
          '<EMAIL>'
        ],
        serverHostKey: [
          'ssh-rsa',
          'ssh-dss',
          'ecdsa-sha2-nistp256',
          'ecdsa-sha2-nistp384',
          'ecdsa-sha2-nistp521'
        ]
      }
    };
    
    // Configurar autenticação
    if (server.privateKey) {
      config.privateKey = server.privateKey;
    } else if (server.password) {
      config.password = server.password;
    } else {
      throw new Error('Nenhum método de autenticação fornecido');
    }
    
    // Conectar ao servidor
    log(`Conectando a ${server.host}:${server.port}...`, server);
    await ssh.connect(config);
    
    report.connection.success = true;
    log(`Conexão estabelecida com sucesso`, server);
    
    // Lista de comandos de diagnóstico
    const diagnosticCommands = [
      { name: 'version', command: 'display version' },
      { name: 'memory', command: 'display memory' },
      { name: 'cpu', command: 'display cpu-usage' },
      { name: 'interfaces', command: 'display interface brief' },
      { name: 'processes', command: 'display process' }
    ];
    
    // Executar comandos de diagnóstico
    for (const cmd of diagnosticCommands) {
      log(`Executando comando: ${cmd.command}`, server);
      
      const result = await executeCommandWithTimeout(ssh, cmd.command);
      
      report.commands.push({
        name: cmd.name,
        command: cmd.command,
        success: result.code === 0,
        stdout: result.stdout,
        stderr: result.stderr,
        code: result.code
      });
      
      if (result.code !== 0) {
        log(`Falha ao executar comando ${cmd.command}: ${result.stderr}`, server);
      }
    }
    
    // Analisar resultados e gerar recomendações
    if (report.commands.some(cmd => cmd.name === 'memory' && cmd.stdout.includes('low'))) {
      report.recommendations.push('O servidor está com pouca memória disponível. Considere reiniciar o dispositivo.');
    }
    
    if (report.commands.some(cmd => cmd.name === 'cpu' && parseInt(cmd.stdout.match(/(\d+)%/) || [0, 0])[1] > 80)) {
      report.recommendations.push('O uso de CPU está alto. Verifique processos em execução e considere otimizar a carga.');
    }
    
  } catch (error) {
    log(`Erro durante o diagnóstico: ${error.message}`, server);
    report.connection.success = false;
    report.connection.error = error.message;
    report.recommendations.push('Falha na conexão SSH. Verifique credenciais e conectividade de rede.');
  } finally {
    // Desconectar
    if (ssh.isConnected()) {
      ssh.dispose();
      log('Conexão SSH encerrada', server);
    }
    
    // Salvar relatório
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    log(`Relatório de diagnóstico salvo em ${reportFile}`, server);
    
    return report;
  }
}

// Função principal
async function main() {
  // Exemplo de servidor para diagnóstico
  // Na prática, isso poderia vir de um banco de dados ou parâmetros de linha de comando
  const server = {
    name: 'Exemplo HarmonyOS',
    host: process.argv[2] || '***********',
    port: parseInt(process.argv[3]) || 22,
    username: process.argv[4] || 'admin',
    password: process.argv[5] || 'password'
  };
  
  if (process.argv.length < 6) {
    console.log('Uso: node harmony-diagnostics.js <host> <port> <username> <password>');
    console.log('Usando valores padrão para demonstração');
  }
  
  try {
    const report = await diagnoseHarmonyServer(server);
    
    console.log('\n===== RESUMO DO DIAGNÓSTICO =====');
    console.log(`Servidor: ${report.server.name} (${report.server.host}:${report.server.port})`);
    console.log(`Conexão: ${report.connection.success ? 'Sucesso' : 'Falha'}`);
    
    if (!report.connection.success) {
      console.log(`Erro de conexão: ${report.connection.error}`);
    } else {
      console.log(`Comandos executados: ${report.commands.length}`);
      console.log(`Comandos com sucesso: ${report.commands.filter(cmd => cmd.success).length}`);
      console.log(`Comandos com falha: ${report.commands.filter(cmd => !cmd.success).length}`);
      
      if (report.recommendations.length > 0) {
        console.log('\nRecomendações:');
        report.recommendations.forEach((rec, index) => {
          console.log(`${index + 1}. ${rec}`);
        });
      }
    }
    
    console.log('\nRelatório completo salvo em:', path.join(REPORT_DIR, `${server.host.replace(/\./g, '_')}_report.json`));
  } catch (error) {
    console.error('Erro ao executar diagnóstico:', error);
  }
}

// Executar script
main();
