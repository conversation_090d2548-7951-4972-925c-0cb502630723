# Prova de Conceito: Serviço Python para Equipamentos de Rede

Este documento detalha uma prova de conceito (PoC) para resolver os problemas de conexão com dispositivos HarmonyOS usando Python, conforme mencionado no plano de migração.

## Estrutura de Arquivos

```
python_poc/
├── requirements.txt
├── main.py
├── app/
│   ├── __init__.py
│   ├── config.py
│   ├── models.py
│   ├── routers/
│   │   ├── __init__.py
│   │   └── ssh.py
│   └── services/
│       ├── __init__.py
│       └── ssh_service.py
└── tests/
    ├── __init__.py
    └── test_ssh.py
```

## Requisitos

```
# requirements.txt
fastapi>=0.68.0
uvicorn>=0.15.0
netmiko>=4.1.0
pydantic>=1.9.0
pytest>=7.0.0
python-dotenv>=0.19.0
```

## Configuração

```python
# app/config.py
import os
from pydantic import BaseSettings
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    app_name: str = "SSH Service"
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Configurações de segurança
    token_secret: str = os.getenv("TOKEN_SECRET", "")
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## Modelos de Dados

```python
# app/models.py
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class SSHCommandRequest(BaseModel):
    host: str
    port: int = 22
    username: str
    password: Optional[str] = None
    private_key: Optional[str] = None
    command: str
    device_type: str = "linux"  # huawei, cisco, etc.
    timeout: int = 30
    
    class Config:
        schema_extra = {
            "example": {
                "host": "***********",
                "port": 22,
                "username": "admin",
                "password": "password123",
                "command": "show version",
                "device_type": "huawei"
            }
        }

class CommandResult(BaseModel):
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    code: Optional[int] = None
    
    class Config:
        schema_extra = {
            "example": {
                "stdout": "HarmonyOS version 2.0\nDevice ID: ABC123",
                "stderr": "",
                "code": 0
            }
        }
```

## Serviço SSH

```python
# app/services/ssh_service.py
import logging
from netmiko import ConnectHandler
from netmiko.ssh_exception import NetMikoTimeoutException, NetMikoAuthenticationException
from ..models import SSHCommandRequest, CommandResult

logger = logging.getLogger(__name__)

class SSHService:
    def __init__(self):
        pass
        
    async def connect_and_execute(self, request: SSHCommandRequest) -> CommandResult:
        """
        Conecta ao dispositivo remoto e executa o comando especificado
        """
        try:
            # Configuração para o Netmiko
            device = {
                'device_type': request.device_type,
                'host': request.host,
                'username': request.username,
                'port': request.port,
                'session_log': f"logs/{request.host}.log",
                'timeout': request.timeout,
            }
            
            # Adicionar credenciais
            if request.password:
                device['password'] = request.password
            elif request.private_key:
                device['key_file'] = request.private_key
            else:
                return CommandResult(
                    stderr="Erro: Nenhum método de autenticação fornecido",
                    code=1
                )
            
            # Conectar e executar comando
            logger.info(f"Conectando a {request.host} com usuário {request.username}")
            
            with ConnectHandler(**device) as conn:
                # Netmiko lida automaticamente com paginação e prompts interativos
                output = conn.send_command(request.command)
                
                return CommandResult(
                    stdout=output,
                    code=0
                )
                
        except NetMikoTimeoutException:
            error_msg = f"Timeout ao conectar a {request.host}"
            logger.error(error_msg)
            return CommandResult(stderr=error_msg, code=1)
            
        except NetMikoAuthenticationException:
            error_msg = f"Falha de autenticação ao conectar a {request.host}"
            logger.error(error_msg)
            return CommandResult(stderr=error_msg, code=1)
            
        except Exception as e:
            error_msg = f"Erro ao executar comando: {str(e)}"
            logger.error(error_msg)
            return CommandResult(stderr=error_msg, code=1)
```

## API Router

```python
# app/routers/ssh.py
from fastapi import APIRouter, HTTPException
from ..models import SSHCommandRequest, CommandResult
from ..services.ssh_service import SSHService

router = APIRouter(
    prefix="/ssh",
    tags=["ssh"],
    responses={404: {"description": "Not found"}},
)

ssh_service = SSHService()

@router.post("/execute", response_model=CommandResult)
async def execute_command(request: SSHCommandRequest):
    """
    Executa um comando SSH em um servidor remoto
    """
    result = await ssh_service.connect_and_execute(request)
    
    if result.code != 0:
        raise HTTPException(status_code=500, detail=result.stderr)
        
    return result
```

## Aplicação FastAPI

```python
# main.py
import logging
import uvicorn
from fastapi import FastAPI
from app.routers import ssh
from app.config import settings

# Configurar logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log")
    ]
)

app = FastAPI(
    title="SSH Service API",
    description="API para conexão SSH com dispositivos de rede (especialmente HarmonyOS)",
    version="0.1.0",
    debug=settings.debug
)

# Registrar routers
app.include_router(ssh.router)

@app.get("/", tags=["root"])
async def read_root():
    return {"message": "SSH Service API está funcionando"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=settings.debug)
```

## Integrando com o Sistema Atual

Para integrar esse serviço Python com o sistema Node.js existente, você pode:

1. Executar o serviço Python em um contêiner Docker separado
2. Adicionar um novo módulo no backend Node.js para se comunicar com o serviço Python

### Exemplo de Código de Integração (Node.js)

```typescript
// Adicionar ao backend/src/services/ssh-python.ts

import axios from 'axios';
import { SSHServer, CommandResult } from '../types/server';

export class PythonSSHService {
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.PYTHON_SSH_SERVICE_URL || 'http://localhost:8000';
  }

  async executeCommand(
    server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>,
    command: string
  ): Promise<CommandResult> {
    try {
      // Determinar o tipo de dispositivo baseado no SO ou nome do servidor
      let deviceType = 'linux';
      if (server.os === 'WINDOWS') {
        deviceType = 'windows';
      } else if (server.name.toLowerCase().includes('huawei') || 
                server.name.toLowerCase().includes('harmony')) {
        deviceType = 'huawei';
      }

      const response = await axios.post(`${this.baseURL}/ssh/execute`, {
        host: server.host,
        port: server.port,
        username: server.username,
        password: server.password,
        private_key: server.privateKey,
        command: command,
        device_type: deviceType
      });

      return {
        stdout: response.data.stdout,
        stderr: response.data.stderr,
        code: response.data.code
      };
    } catch (error) {
      console.error('Erro ao executar comando via serviço Python:', error);
      
      if (error.response) {
        return {
          stderr: `Erro do serviço Python: ${error.response.data.detail || 'Desconhecido'}`,
          code: error.response.status
        };
      }
      
      return {
        stderr: `Falha ao conectar ao serviço Python: ${error.message}`,
        code: 500
      };
    }
  }
}
```

## Docker Compose para o Serviço Python

```yaml
# Adicionar ao docker-compose.yml existente

services:
  # ... serviços existentes ...
  
  python-ssh:
    build:
      context: ./python_poc
      dockerfile: Dockerfile
    container_name: python-ssh-service
    ports:
      - "8000:8000"
    volumes:
      - ./python_poc:/app
      - ./python_poc/logs:/app/logs
    environment:
      - DEBUG=true
      - LOG_LEVEL=INFO
      - TOKEN_SECRET=${TOKEN_SECRET}
    restart: unless-stopped
    networks:
      - app-network
```

## Dockerfile para o Serviço Python

```dockerfile
# python_poc/Dockerfile
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Criar diretório de logs
RUN mkdir -p logs

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## Estratégia de Implantação

1. **Desenvolvimento e teste do serviço Python**: Implementar e testar localmente
2. **Configuração da estrutura de containers**: Adicionar o serviço Python ao docker-compose.yml
3. **Integração gradual**:
   - Primeiro, usar o serviço Python apenas para dispositivos HarmonyOS problemáticos
   - Gradualmente expandir para outros dispositivos problemáticos
   - Coletar métricas de estabilidade e performance

4. **Monitoramento**:
   - Implementar logging detalhado
   - Criar dashboards para monitorar estabilidade
   - Comparar taxas de falha entre Node.js e Python

Este protótipo resolve os problemas específicos reportados com HarmonyOS e oferece uma base para expansão futura, permitindo uma migração gradual e com menor risco.