/**
 * Interface para configuração de conexão SSH
 * Contém todos os parâmetros necessários para estabelecer uma conexão SSH
 */
export interface SSHConfig {
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  readyTimeout: number;
  keepaliveInterval: number;
  keepaliveCountMax?: number;
  reconnect?: boolean;
  reconnectDelay?: number;
  reconnectTries?: number;
  algorithms?: {
    kex?: string[];
    cipher?: string[];
    serverHostKey?: string[];
  };
}
