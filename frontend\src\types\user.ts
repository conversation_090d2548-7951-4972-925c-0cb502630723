export type Role = 'ADMIN' | 'USER'

export interface User {
  id: string
  name: string
  email: string
  role: Role
  active: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateUserDTO {
  name: string
  email: string
  password: string
  role?: Role
  active?: boolean
}

export interface UpdateUserDTO {
  name?: string
  email?: string
  password?: string
  role?: Role
  active?: boolean
}

export interface LoginResponse {
  token: string
}

export interface UserResponse {
  user: User
} 